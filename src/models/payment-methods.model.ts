// payment-methods-model.ts - A mongoose model
import {Application} from '../declarations'
import {Model} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'paymentMethods'
  const mongooseClient = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      userId: {type: String, index: true},
      schoolId: {type: String, ref: 'schools', index: true},
      stripeCustomerId: {type: String, required: true, index: true},
      stripePaymentMethodId: {type: String, required: true},
      cardBrand: {type: String, required: true},
      last4: {type: String, required: true},
      expMonth: {type: Number, required: true},
      expYear: {type: Number, required: true},
      fingerprint: {type: String},
      isDefault: {type: Boolean, default: false},
    },
    {
      timestamps: true,
    }
  )

  schema.index(
    {stripePaymentMethodId: 1},
    {
      unique: true,
      partialFilterExpression: {
        stripePaymentMethodId: {$ne: 'customer_record'},
      },
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
