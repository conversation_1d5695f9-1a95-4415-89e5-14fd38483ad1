// stripe-webhook.model.ts - A mongoose model
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'stripeWebhook'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      eventId: {type: String, required: true, unique: true},
      eventType: {type: String, required: true},
      data: {type: Schema.Types.Mixed, required: true},
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
