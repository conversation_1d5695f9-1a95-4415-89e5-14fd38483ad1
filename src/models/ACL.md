```js
// hooks/access-control.js

const {Forbidden, BadRequest} = require('@feathersjs/errors')

/**
 * Universal access control hook for uid-based resources (orders, income-logs, etc.)
 * Handles both user and school contexts with admin verification
 */
const accessControl = (options = {}) => {
  const {
    uidField = 'uid', // Field that contains user/school ID
    isSchoolField = 'isSchool', // Field that indicates if it's a school record
    allowCreate = false, // Whether to apply to create operations
    allowUpdate = false, // Whether to apply to update operations
    adminRoles = ['admin', 'school-admin'], // Roles that can access school data
  } = options

  return async (context) => {
    const {app, method, type, params, data, id} = context

    // Only apply to 'before' hooks for find/get and optionally create/update
    if (type !== 'before') return context

    // Apply to find, get, and optionally create/update
    const applicableMethods = ['find', 'get']
    if (allowCreate) applicableMethods.push('create')
    if (allowUpdate) applicableMethods.push('update', 'patch')

    if (!applicableMethods.includes(method)) return context

    // Ensure user is authenticated
    if (!params.user) {
      throw new Forbidden('Authentication required')
    }

    const currentUserId = params.user._id || params.user.id

    // Handle different methods
    switch (method) {
      case 'find':
        return handleFind(context, currentUserId, uidField, isSchoolField, adminRoles, app)
      case 'get':
        return handleGet(context, currentUserId, uidField, isSchoolField, adminRoles, app)
      case 'create':
        return handleCreate(context, currentUserId, uidField, isSchoolField, adminRoles, app)
      case 'update':
      case 'patch':
        return handleUpdate(context, currentUserId, uidField, isSchoolField, adminRoles, app)
      default:
        return context
    }
  }
}

// Handle find operations
const handleFind = async (context, currentUserId, uidField, isSchoolField, adminRoles, app) => {
  const {params} = context

  // Get user's accessible school IDs
  const accessibleSchoolIds = await getUserAccessibleSchools(currentUserId, adminRoles, app)

  // Build query filter
  const accessFilter = {
    $or: [
      // User's own records
      {[uidField]: currentUserId, [isSchoolField]: {$ne: true}},
      // School records where user is admin
      ...(accessibleSchoolIds.length > 0
        ? [
            {
              [uidField]: {$in: accessibleSchoolIds},
              [isSchoolField]: true,
            },
          ]
        : []),
    ],
  }

  // Merge with existing query
  if (params.query) {
    params.query = {
      $and: [params.query, accessFilter],
    }
  } else {
    params.query = accessFilter
  }

  return context
}

// Handle get operations
const handleGet = async (context, currentUserId, uidField, isSchoolField, adminRoles, app) => {
  const {id, service} = context

  try {
    // Get the record first (without restrictions)
    const record = await service._get(id, {provider: null})

    if (!record) {
      throw new BadRequest('Record not found')
    }

    // Check access
    const hasAccess = await checkRecordAccess(record, currentUserId, uidField, isSchoolField, adminRoles, app)

    if (!hasAccess) {
      throw new Forbidden('Access denied to this record')
    }

    return context
  } catch (error) {
    if (error.name === 'Forbidden' || error.name === 'BadRequest') {
      throw error
    }
    throw new BadRequest('Unable to verify access')
  }
}

// Handle create operations
const handleCreate = async (context, currentUserId, uidField, isSchoolField, adminRoles, app) => {
  const {data} = context
  const records = Array.isArray(data) ? data : [data]

  for (const record of records) {
    const hasAccess = await checkCreateAccess(record, currentUserId, uidField, isSchoolField, adminRoles, app)

    if (!hasAccess) {
      throw new Forbidden('Access denied to create this record')
    }
  }

  return context
}

// Handle update operations
const handleUpdate = async (context, currentUserId, uidField, isSchoolField, adminRoles, app) => {
  const {id, service} = context

  try {
    // Get existing record
    const existingRecord = await service._get(id, {provider: null})

    const hasAccess = await checkRecordAccess(existingRecord, currentUserId, uidField, isSchoolField, adminRoles, app)

    if (!hasAccess) {
      throw new Forbidden('Access denied to update this record')
    }

    return context
  } catch (error) {
    if (error.name === 'Forbidden') {
      throw error
    }
    throw new BadRequest('Unable to verify update access')
  }
}

// Helper: Check if user has access to a specific record
const checkRecordAccess = async (record, currentUserId, uidField, isSchoolField, adminRoles, app) => {
  const recordUid = record[uidField]
  const recordIsSchool = record[isSchoolField]

  // If it's a user record, check if it belongs to current user
  if (!recordIsSchool) {
    return recordUid === currentUserId
  }

  // If it's a school record, check if user is admin of that school
  return await isUserSchoolAdmin(currentUserId, recordUid, adminRoles, app)
}

// Helper: Check create access
const checkCreateAccess = async (record, currentUserId, uidField, isSchoolField, adminRoles, app) => {
  const recordUid = record[uidField]
  const recordIsSchool = record[isSchoolField]

  // If creating for user, must be current user
  if (!recordIsSchool) {
    return recordUid === currentUserId
  }

  // If creating for school, user must be admin of that school
  return await isUserSchoolAdmin(currentUserId, recordUid, adminRoles, app)
}

// Helper: Get all school IDs that user has admin access to
const getUserAccessibleSchools = async (userId, adminRoles, app) => {
  try {
    // This depends on your school-user relationship model
    // Option 1: If you have a school-admins junction table
    const schoolAdmins = await app.service('school-admins').find({
      query: {
        userId,
        role: {$in: adminRoles},
        status: 'active', // or however you track active admins
      },
      paginate: false,
      provider: null,
    })

    return schoolAdmins.map((admin) => admin.schoolId)

    // Option 2: If school has admins array field
    // const schools = await app.service('school-plans').find({
    //   query: {
    //     'admins.userId': userId,
    //     'admins.role': { $in: adminRoles }
    //   },
    //   paginate: false,
    //   provider: null
    // });
    // return schools.map(school => school._id);
  } catch (error) {
    console.error('Error getting user accessible schools:', error)
    return []
  }
}

// Helper: Check if user is admin of specific school
const isUserSchoolAdmin = async (userId, schoolId, adminRoles, app) => {
  try {
    // Option 1: Junction table approach
    const adminRecord = await app.service('school-admins').find({
      query: {
        userId,
        schoolId,
        role: {$in: adminRoles},
        status: 'active',
      },
      paginate: false,
      provider: null,
    })

    return adminRecord.length > 0

    // Option 2: Embedded admins approach
    // const school = await app.service('school-plans').get(schoolId, { provider: null });
    // return school.admins?.some(admin =>
    //   admin.userId === userId &&
    //   adminRoles.includes(admin.role) &&
    //   admin.status === 'active'
    // );
  } catch (error) {
    console.error('Error checking school admin access:', error)
    return false
  }
}

// Export the main hook and helpers
module.exports = {
  accessControl,
  checkRecordAccess,
  getUserAccessibleSchools,
  isUserSchoolAdmin,
}

// USAGE EXAMPLES:

// orders.hooks.js
const {accessControl} = require('../hooks/access-control')

module.exports = {
  before: {
    find: [accessControl()],
    get: [accessControl()],
    create: [accessControl({allowCreate: true})],
    update: [accessControl({allowUpdate: true})],
    patch: [accessControl({allowUpdate: true})],
  },
}

// income-logs.hooks.js
module.exports = {
  before: {
    find: [
      accessControl({
        uidField: 'uid',
        isSchoolField: 'isSchool',
        adminRoles: ['admin', 'school-admin'],
      }),
    ],
    get: [
      accessControl({
        uidField: 'uid',
        isSchoolField: 'isSchool',
      }),
    ],
  },
}

// For services with different field names
// some-other-service.hooks.js
module.exports = {
  before: {
    find: [
      accessControl({
        uidField: 'ownerId',
        isSchoolField: 'isSchoolOwned',
        adminRoles: ['super-admin'],
      }),
    ],
  },
}
```
