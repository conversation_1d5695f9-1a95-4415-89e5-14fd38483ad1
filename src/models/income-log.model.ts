// income-log-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'incomeLog'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      uid: {type: String, required: true},
      isSchool: {type: Boolean, default: false},
      tab: {type: String, enum: ['earn', 'claim'], default: 'earn'},
      category: {
        type: String,
        required: true,
        enum: [
          'library_content',
          'new_prompt_content',
          'self_study_content',
          'premium_contents_unit_module',
          'premium_contents_task_sessions',
          'premium_content_audit',
          'teaching_service',
          'correcting_service',
          'associated_task',
          'certificate',
        ],
      },
      value: {type: Number, required: true},
      total: {type: Number, required: true},
      expected: {type: Number},
      actualAt: {type: Date},
      status: {type: Number, enum: [0, 1], default: 1},
      isParent: {type: Boolean},
      businessId: {type: String},
      event_details: {type: Schema.Types.Mixed},
      session_status: {type: String, enum: ['ongoing', 'completed', 'cancelled']},
      notes: {type: String},
      isBonusSettle: {type: Boolean, default: false},
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
