// gift-card-log-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'giftCardLog'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      uid: {type: String, required: true}, // user._id or school-plan._id
      tab: {type: String, enum: ['earn', 'claim']}, // earn: add to balance, claim: use from balance
      source: {type: String, required: true}, // order, refund, redeem, gift
      category: {type: String, required: true}, // Specific category within source (e.g., 'order_payment', 'gift_card_redeem')
      value: {type: Number, required: true}, // Amount changed in this transaction
      total: {type: Number, required: true}, // Total balance after this transaction
      businessId: {type: String}, // Related entity ID (order._id, gift-card._id)
      snapshot: {type: Object}, // Related entity snapshot
      isSchool: {type: Boolean, default: false}, // Whether this is for a school account
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
