import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'PayoutTransaction'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient

  const payoutTransactionSchema = new Schema(
    {
      userId: {
        type: String,
        required: function (this: any) {
          return !this.schoolId
        },
        validate: {
          validator: function (this: any, value: any) {
            // Ensure only one of userId or schoolId is set
            return !value || !this.schoolId
          },
          message: 'Cannot have both userId and schoolId',
        },
      },

      schoolId: {
        type: String,
        required: function (this: any) {
          return !this.userId
        },
        validate: {
          validator: function (this: any, value: any) {
            // Ensure only one of userId or schoolId is set
            return !value || !this.userId
          },
          message: 'Cannot have both userId and schoolId',
        },
      },

      airwallexBeneficiaryId: {
        type: String,
        required: true,
      },

      amount: {
        type: Number,
        required: true,
        min: 500, // minimum $5.00 in cents
      },

      userBalanceAtSnapshot: {
        type: Number,
        required: true,
      },

      snapshotDate: {
        type: Date,
        required: true,
        index: true,
      },

      scheduledPayoutDate: {
        type: Date,
        required: true,
        index: true,
      },

      status: {
        type: String,
        enum: ['pending', 'cancelled', 'processing', 'completed', 'failed'],
        default: 'pending',
        required: true,
        index: true,
      },

      // Transaction details
      airwallexTransactionId: {
        type: String,
        sparse: true, // only for completed transactions
      },

      airwallexResponse: {
        type: Schema.Types.Mixed,
        default: null,
      },

      errorDetails: {
        type: Schema.Types.Mixed,
        default: null,
      },

      // Timestamps
      sentAt: {
        type: Date,
        default: null,
      },

      completedAt: {
        type: Date,
        default: null,
      },

      // Admin actions
      cancelledBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        default: null,
      },

      adminNotes: {
        type: String,
        default: null,
      },

      // For reporting/grouping
      payoutMonth: {
        type: String,
        required: true,
        index: true, // for efficient monthly queries
      },

      payoutYear: {
        type: Number,
        required: true,
        index: true,
      },
    },
    {
      timestamps: true, // Automatically adds createdAt and updatedAt
      collection: 'payout-transactions',
    }
  )

  // Compound indexes for better query performance
  payoutTransactionSchema.index({status: 1, scheduledPayoutDate: 1}) // for processing queries
  payoutTransactionSchema.index({userId: 1, createdAt: -1}) // for user payout history
  payoutTransactionSchema.index({schoolId: 1, createdAt: -1}) // for school payout history
  payoutTransactionSchema.index({payoutMonth: 1, status: 1}) // for monthly admin dashboard
  payoutTransactionSchema.index({status: 1, payoutYear: 1, payoutMonth: 1}) // for admin analytics

  // Check if model already exists to prevent re-compilation errors
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }

  const payoutTransactionModel = mongooseClient.model(modelName, payoutTransactionSchema)

  return payoutTransactionModel
}
