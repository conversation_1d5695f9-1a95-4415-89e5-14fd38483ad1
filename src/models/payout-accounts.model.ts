import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'PayoutAccount'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient

  const payoutAccountSchema = new Schema(
    {
      // Reference to either user or school (one must be present, not both)
      userId: {
        type: String,
        required: function (this: any) {
          return !this.schoolId
        },
        validate: {
          validator: function (this: any, value: any) {
            // Ensure only one of userId or schoolId is set
            return !value || !this.schoolId
          },
          message: 'Cannot have both userId and schoolId',
        },
      },

      schoolId: {
        type: String,
        required: function (this: any) {
          return !this.userId
        },
        validate: {
          validator: function (this: any, value: any) {
            // Ensure only one of userId or schoolId is set
            return !value || !this.userId
          },
          message: 'Cannot have both userId and schoolId',
        },
      },

      // Airwallex beneficiary ID
      airwallexBeneficiaryId: {
        type: String,
        required: true,
        unique: true,
        index: true,
      },

      // Payout settings
      minimumPayoutAmount: {
        type: Number,
        default: 500, // $5 in cents
        min: 100, // Minimum $1
        max: 10000, // Maximum $100 minimum
      },

      // Payout statistics
      lastPayoutDate: {
        type: Date,
        index: true,
      },

      totalPayoutAmount: {
        type: Number,
        default: 0,
        min: 0,
      },

      payoutCount: {
        type: Number,
        default: 0,
        min: 0,
      },

      // Soft delete flag
      isActive: {
        type: Boolean,
        default: true,
        index: true,
      },
    },
    {
      timestamps: true, // Automatically adds createdAt and updatedAt
      collection: 'payout-accounts',
    }
  )

  // Compound indexes for efficient queries
  payoutAccountSchema.index({userId: 1, isActive: 1})
  payoutAccountSchema.index({schoolId: 1, isActive: 1})
  payoutAccountSchema.index({accountStatus: 1, isActive: 1})

  // Check if model already exists to prevent re-compilation errors
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }

  const payoutAccountModel = mongooseClient.model(modelName, payoutAccountSchema)

  return payoutAccountModel
}
