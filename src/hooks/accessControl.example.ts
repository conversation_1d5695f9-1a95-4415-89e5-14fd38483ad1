/**
 * Example usage of the AccessControl system
 * This file demonstrates how to use the new access control utilities
 */

import { Application } from '../declarations'
import { createAccessControl, accessControl } from './accessControl'

// Example 1: Using AccessControl in a service class
export class ExampleService {
  private app: Application
  private accessControl: any

  constructor(app: Application) {
    this.app = app
    this.accessControl = createAccessControl(app)
  }

  async getUserSchools(userId: string) {
    // Get all schools the user has access to
    const accessibleSchools = await this.accessControl.getUserAccessibleSchools(userId)
    return accessibleSchools
  }

  async checkSchoolPermission(userId: string, schoolId: string) {
    // Check if user has access to a specific school
    const hasAccess = await this.accessControl.hasSchoolAccess(userId, schoolId)
    return hasAccess
  }

  async getDetailedAccess(userId: string, schoolId: string) {
    // Get detailed access information
    const accessDetails = await this.accessControl.getSchoolAccessDetails(userId, schoolId)
    console.log('Access details:', accessDetails)
    // Output: { hasAccess: true, accessType: 'admin', schoolId: '...' }
    return accessDetails
  }

  async validateSchoolOperation(userId: string, schoolId: string, operation: 'read' | 'write') {
    // Check if user can perform specific operations
    const canAct = await this.accessControl.canActForSchool(userId, schoolId, operation)
    return canAct
  }
}

// Example 2: Using AccessControl hooks in service hooks
export const exampleHooks = {
  before: {
    // Basic access control for orders
    find: [
      accessControl({
        uidField: 'buyer',
        isSchoolField: 'isSchool',
        adminRoles: ['admin', 'school-admin'],
      }),
    ],
    
    // Access control for income logs
    get: [
      accessControl({
        uidField: 'uid',
        isSchoolField: 'isSchool',
        adminRoles: ['admin', 'school-admin'],
      }),
    ],

    // Access control with create permissions
    create: [
      accessControl({
        uidField: 'uid',
        isSchoolField: 'isSchool',
        allowCreate: true,
        adminRoles: ['admin', 'school-admin'],
      }),
    ],

    // Access control with update permissions
    patch: [
      accessControl({
        uidField: 'uid',
        isSchoolField: 'isSchool',
        allowUpdate: true,
        adminRoles: ['admin', 'school-admin'],
      }),
    ],
  },
}

// Example 3: Manual access control in custom hooks
export const customAccessControlHook = () => {
  return async (context: any) => {
    const { app, params } = context
    const user = params.user
    
    if (!user) {
      throw new Error('Authentication required')
    }

    const accessControl = createAccessControl(app)
    
    // Custom logic for specific use cases
    const hasAnySchoolAccess = await accessControl.hasAnySchoolAccess(user._id)
    
    if (!hasAnySchoolAccess) {
      // User has no school access, apply user-only filters
      context.params.query = {
        ...context.params.query,
        uid: user._id,
        isSchool: { $ne: true },
      }
    } else {
      // User has school access, get accessible schools
      const accessibleSchools = await accessControl.getUserAccessibleSchools(user._id)
      
      context.params.query = {
        ...context.params.query,
        $or: [
          // User's own records
          { uid: user._id, isSchool: { $ne: true } },
          // School records where user is admin/coordinator/contact
          { uid: { $in: accessibleSchools }, isSchool: true },
        ],
      }
    }

    return context
  }
}

// Example 4: Using in different collection categories
export const categorySpecificAccess = {
  // Category 1: Orders, Gift Cards (admin or coordinator access)
  category1: accessControl({
    uidField: 'buyer', // or 'uid' depending on collection
    isSchoolField: 'isSchool',
    adminRoles: ['admin', 'school-admin'], // Will check school-user and subjects coordinator
  }),

  // Category 2: Income Logs, Payment Methods (contact access)
  category2: accessControl({
    uidField: 'uid',
    isSchoolField: 'isSchool',
    adminRoles: ['admin', 'school-admin'], // Will check school-plan contact
  }),
}

// Example 5: Programmatic access control checks
export async function exampleAccessChecks(app: Application) {
  const accessControl = createAccessControl(app)
  const userId = 'user123'
  const schoolId = 'school456'

  // Check basic access
  const hasAccess = await accessControl.hasSchoolAccess(userId, schoolId)
  console.log('Has access:', hasAccess)

  // Get all accessible schools
  const schools = await accessControl.getUserAccessibleSchools(userId)
  console.log('Accessible schools:', schools)

  // Get detailed access info
  const details = await accessControl.getSchoolAccessDetails(userId, schoolId)
  console.log('Access details:', details)

  // Check operation permissions
  const canRead = await accessControl.canActForSchool(userId, schoolId, 'read')
  const canWrite = await accessControl.canActForSchool(userId, schoolId, 'write')
  console.log('Can read:', canRead, 'Can write:', canWrite)

  // Check if user has any school access
  const hasAnyAccess = await accessControl.hasAnySchoolAccess(userId)
  console.log('Has any school access:', hasAnyAccess)
}
