// hooks/access-control.ts

import {HookContext, Hook} from '@feathersjs/feathers'
import {Forbidden, BadRequest} from '@feathersjs/errors'
import hook from '../hook'

interface AccessControlOptions {
  uidField?: string
  isSchoolField?: string
  allowCreate?: boolean
  allowUpdate?: boolean
  adminRoles?: string[]
  managerRoles?: string[]
}

interface User {
  _id?: string
  id?: string
  role?: string
  roles?: string[]
  [key: string]: any
}

interface QueryWithSys {
  $sys?: boolean
  [key: string]: any
}

interface SchoolUserRecord {
  _id?: string
  uid: string
  school: string
  email: string
  role: string[]
  status: number
  del: boolean
  [key: string]: any
}

interface SubjectRecord {
  _id?: string
  uid: string
  name: string
  coordinator: string[]
  del: boolean
  [key: string]: any
}

interface SchoolPlanRecord {
  _id?: string
  name: string
  contact?: string
  status: number
  [key: string]: any
}

interface AccessCheckResult {
  hasAccess: boolean
  accessType?: 'admin' | 'coordinator' | 'contact'
  schoolId?: string
}

/**
 * Universal access control hook for uid-based resources (orders, income-logs, etc.)
 * Handles both user and school contexts with admin verification
 * Includes system admin bypass logic
 */
const accessControl = (options: AccessControlOptions = {}): Hook => {
  const {
    uidField = 'uid',
    isSchoolField = 'isSchool',
    allowCreate = false,
    allowUpdate = false,
    adminRoles = ['admin', 'school-admin'],
    managerRoles = ['manager', 'school-manager'],
  } = options

  return async (context: HookContext): Promise<HookContext> => {
    const {app, method, type, params, data, id} = context

    // Only apply to 'before' hooks for find/get and optionally create/update
    if (type !== 'before') return context

    // Apply to find, get, and optionally create/update
    const applicableMethods = ['find', 'get']
    if (allowCreate) applicableMethods.push('create')
    if (allowUpdate) applicableMethods.push('update', 'patch')

    if (!applicableMethods.includes(method)) return context

    // Ensure user is authenticated
    if (!params.user) {
      throw new Forbidden('Authentication required')
    }

    const currentUser = params.user as User
    const currentUserId = currentUser._id

    if (!currentUserId) {
      throw new Forbidden('Authentication required')
    }

    // Handle system admin bypass logic
    if (method === 'find' && params.query) {
      const query = params.query as QueryWithSys

      if (query.$sys) {
        // Check if user has system admin or manager access
        const hasSystemAccess = hook.roleHas(['sys', 'admin'])(context) || hook.managerRoleHas(managerRoles)(context)

        if (!hasSystemAccess) {
          context.result = null
          return context
        }

        // Remove $sys from query and allow full access
        delete query.$sys
        return context
      }
    }

    // Handle different methods with regular access control
    switch (method) {
      case 'find':
        return handleFind(context, currentUserId, uidField, isSchoolField, adminRoles, app)
      case 'get':
        return handleGet(context, currentUserId, uidField, isSchoolField, adminRoles, app)
      case 'create':
        return handleCreate(context, currentUserId, uidField, isSchoolField, adminRoles, app)
      case 'update':
      case 'patch':
        return handleUpdate(context, currentUserId, uidField, isSchoolField, adminRoles, app)
      default:
        return context
    }
  }
}

// Handle find operations
const handleFind = async (
  context: HookContext,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  adminRoles: string[],
  app: any
): Promise<HookContext> => {
  const {params} = context

  // Get user's accessible school IDs
  const accessibleSchoolIds = await getUserAccessibleSchools(currentUserId, adminRoles, app)

  // Build query filter
  const accessFilter = {
    $or: [
      // User's own records
      {[uidField]: currentUserId, [isSchoolField]: {$ne: true}},
      // School records where user is admin
      ...(accessibleSchoolIds.length > 0
        ? [
            {
              [uidField]: {$in: accessibleSchoolIds},
              [isSchoolField]: true,
            },
          ]
        : []),
    ],
  }

  // Merge with existing query
  if (params.query) {
    params.query = {
      ...params.query,
      ...accessFilter,
    }
  } else {
    params.query = accessFilter
  }

  return context
}

// Handle get operations
const handleGet = async (
  context: HookContext,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  adminRoles: string[],
  app: any
): Promise<HookContext> => {
  const {id, service} = context

  try {
    // Get the record first (without restrictions)
    const record = await service._get(id, {provider: null})

    if (!record) {
      throw new BadRequest('Record not found')
    }

    // Check access
    const hasAccess = await checkRecordAccess(record, currentUserId, uidField, isSchoolField, adminRoles, app)

    if (!hasAccess) {
      throw new Forbidden('Access denied to this record')
    }

    return context
  } catch (error: any) {
    if (error.name === 'Forbidden' || error.name === 'BadRequest') {
      throw error
    }
    throw new BadRequest('Unable to verify access')
  }
}

// Handle create operations
const handleCreate = async (
  context: HookContext,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  adminRoles: string[],
  app: any
): Promise<HookContext> => {
  const {data} = context
  const records = Array.isArray(data) ? data : [data]

  for (const record of records) {
    const hasAccess = await checkCreateAccess(record, currentUserId, uidField, isSchoolField, adminRoles, app)

    if (!hasAccess) {
      throw new Forbidden('Access denied to create this record')
    }
  }

  return context
}

// Handle update operations
const handleUpdate = async (
  context: HookContext,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  adminRoles: string[],
  app: any
): Promise<HookContext> => {
  const {id, service} = context

  try {
    // Get existing record
    const existingRecord = await service._get(id, {provider: null})

    const hasAccess = await checkRecordAccess(existingRecord, currentUserId, uidField, isSchoolField, adminRoles, app)

    if (!hasAccess) {
      throw new Forbidden('Access denied to update this record')
    }

    return context
  } catch (error: any) {
    if (error.name === 'Forbidden') {
      throw error
    }
    throw new BadRequest('Unable to verify update access')
  }
}

// Helper: Check if user has access to a specific record
const checkRecordAccess = async (
  record: any,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  adminRoles: string[],
  app: any
): Promise<boolean> => {
  const recordUid = record[uidField]
  const recordIsSchool = record[isSchoolField]

  // If it's a user record, check if it belongs to current user
  if (!recordIsSchool) {
    return recordUid === currentUserId
  }

  // If it's a school record, check if user is admin of that school
  return await hasSchoolAccess(currentUserId, recordUid, adminRoles, app)
}

// Helper: Check create access
const checkCreateAccess = async (
  record: any,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  adminRoles: string[],
  app: any
): Promise<boolean> => {
  const recordUid = record[uidField]
  const recordIsSchool = record[isSchoolField]

  // If creating for user, must be current user
  if (!recordIsSchool) {
    return recordUid === currentUserId
  }

  // If creating for school, user must be admin of that school
  return await hasSchoolAccess(currentUserId, recordUid, adminRoles, app)
}

// Helper: Get all school IDs that user has admin access to
const getUserAccessibleSchools = async (userId: string, adminRoles: string[], app: any): Promise<string[]> => {
  try {
    const schoolIds = new Set<string>()

    // Category 1: Check if user is admin of school via 'school-user' collection
    // 'school-user' uid: user._id, status: 2, role: 'admin'
    const schoolUserService = app.service('school-user')
    const schoolUserRecords = await schoolUserService.find({
      query: {
        uid: userId,
        status: 2, // success status
        role: {$in: adminRoles}, // admin roles
        del: {$ne: true}, // not deleted
      },
      paginate: false,
      provider: null, // bypass hooks
    })

    // Add school IDs from school-user records
    if (schoolUserRecords && Array.isArray(schoolUserRecords)) {
      schoolUserRecords.forEach((record: any) => {
        if (record.school) {
          schoolIds.add(record.school)
        }
      })
    }

    // Category 1: Check if user is a subject coordinator
    // 'subjects' coordinator: user._id, del: false, here uid is school id
    const subjectsService = app.service('subjects')
    const subjectRecords = await subjectsService.find({
      query: {
        coordinator: userId,
        del: {$ne: true}, // not deleted
      },
      paginate: false,
      provider: null, // bypass hooks
    })

    // Add school IDs from subjects where user is coordinator
    if (subjectRecords && Array.isArray(subjectRecords)) {
      subjectRecords.forEach((record: any) => {
        if (record.uid) {
          schoolIds.add(record.uid) // uid is school id in subjects collection
        }
      })
    }

    // Category 2: Check if user is main contact of the school
    // 'school-plan' contact: user._id
    const schoolPlanService = app.service('school-plan')
    const schoolPlanRecords = await schoolPlanService.find({
      query: {
        contact: userId,
      },
      paginate: false,
      provider: null, // bypass hooks
    })

    // Add school IDs from school-plan where user is contact
    if (schoolPlanRecords && Array.isArray(schoolPlanRecords)) {
      schoolPlanRecords.forEach((record: any) => {
        if (record._id) {
          schoolIds.add(record._id.toString())
        }
      })
    }

    return Array.from(schoolIds)
  } catch (error) {
    console.error('Error getting user accessible schools:', error)
    return []
  }
}

// Helper: Check if user is admin of specific school
const hasSchoolAccess = async (userId: string, schoolId: string, adminRoles: string[], app: any): Promise<boolean> => {
  try {
    // Check if user is admin of school via 'school-user' collection
    const schoolUserService = app.service('school-user')
    const schoolUserRecord = await schoolUserService.find({
      query: {
        uid: userId,
        school: schoolId,
        status: 2, // success status
        role: {$in: adminRoles}, // admin roles
        del: {$ne: true}, // not deleted
      },
      paginate: false,
      provider: null, // bypass hooks
    })

    if (schoolUserRecord && Array.isArray(schoolUserRecord) && schoolUserRecord.length > 0) {
      return true
    }

    // Check if user is a subject coordinator for this school
    const subjectsService = app.service('subjects')
    const subjectRecord = await subjectsService.find({
      query: {
        uid: schoolId, // uid is school id in subjects collection
        coordinator: userId,
        del: {$ne: true}, // not deleted
      },
      paginate: false,
      provider: null, // bypass hooks
    })

    if (subjectRecord && Array.isArray(subjectRecord) && subjectRecord.length > 0) {
      return true
    }

    // Check if user is main contact of the school
    const schoolPlanService = app.service('school-plan')
    const schoolPlan = await schoolPlanService.get(schoolId, {
      provider: null, // bypass hooks
    })

    if (schoolPlan && schoolPlan.contact === userId) {
      return true
    }

    return false
  } catch (error) {
    console.error('Error checking school admin access:', error)
    return false
  }
}

/**
 * Centralized Access Control utility
 * Provides reusable functions for school access control across the application
 */
class AccessControlUtil {
  private app: any

  constructor(app: any) {
    this.app = app
  }

  /**
   * Get all school IDs that user has admin access to
   * Checks multiple sources: school-user, subjects coordinator, school-plan contact
   */
  async getUserAccessibleSchools(userId: string, adminRoles: string[] = ['admin', 'school-admin']): Promise<string[]> {
    return getUserAccessibleSchools(userId, adminRoles, this.app)
  }

  /**
   * Check if user has access to a specific school
   * Checks multiple sources: school-user admin, subjects coordinator, school-plan contact
   */
  async hasSchoolAccess(userId: string, schoolId: string, adminRoles: string[] = ['admin', 'school-admin']): Promise<boolean> {
    return hasSchoolAccess(userId, schoolId, adminRoles, this.app)
  }

  /**
   * Check if user has access to a specific record
   * Handles both user and school contexts
   */
  async checkRecordAccess(
    record: any,
    currentUserId: string,
    uidField: string = 'uid',
    isSchoolField: string = 'isSchool',
    adminRoles: string[] = ['admin', 'school-admin']
  ): Promise<boolean> {
    return checkRecordAccess(record, currentUserId, uidField, isSchoolField, adminRoles, this.app)
  }

  /**
   * Get access control hook with default options
   */
  getAccessControlHook(options: AccessControlOptions = {}): Hook {
    return accessControl(options)
  }

  /**
   * Check if user has access to any schools
   */
  async hasAnySchoolAccess(userId: string, adminRoles: string[] = ['admin', 'school-admin']): Promise<boolean> {
    const accessibleSchools = await this.getUserAccessibleSchools(userId, adminRoles)
    return accessibleSchools.length > 0
  }

  /**
   * Get detailed access information for a user and school
   */
  async getSchoolAccessDetails(userId: string, schoolId: string, adminRoles: string[] = ['admin', 'school-admin']): Promise<AccessCheckResult> {
    try {
      // Check school-user admin access
      const schoolUserService = this.app.service('school-user')
      const schoolUserRecord = await schoolUserService.find({
        query: {
          uid: userId,
          school: schoolId,
          status: 2,
          role: {$in: adminRoles},
          del: {$ne: true},
        },
        paginate: false,
        provider: null,
      })

      if (schoolUserRecord && Array.isArray(schoolUserRecord) && schoolUserRecord.length > 0) {
        return {
          hasAccess: true,
          accessType: 'admin',
          schoolId,
        }
      }

      // Check subject coordinator access
      const subjectsService = this.app.service('subjects')
      const subjectRecord = await subjectsService.find({
        query: {
          uid: schoolId,
          coordinator: userId,
          del: {$ne: true},
        },
        paginate: false,
        provider: null,
      })

      if (subjectRecord && Array.isArray(subjectRecord) && subjectRecord.length > 0) {
        return {
          hasAccess: true,
          accessType: 'coordinator',
          schoolId,
        }
      }

      // Check main contact access
      const schoolPlanService = this.app.service('school-plan')
      const schoolPlan = await schoolPlanService.get(schoolId, {
        provider: null,
      })

      if (schoolPlan && schoolPlan.contact === userId) {
        return {
          hasAccess: true,
          accessType: 'contact',
          schoolId,
        }
      }

      return {
        hasAccess: false,
      }
    } catch (error) {
      console.error('Error getting school access details:', error)
      return {
        hasAccess: false,
      }
    }
  }

  /**
   * Validate if a user can perform operations on behalf of a school
   * Useful for order creation, payment processing, etc.
   */
  async canActForSchool(userId: string, schoolId: string, operation: 'read' | 'write' = 'read'): Promise<boolean> {
    const accessDetails = await this.getSchoolAccessDetails(userId, schoolId)

    if (!accessDetails.hasAccess) {
      return false
    }

    // For write operations, require admin or contact access
    if (operation === 'write') {
      return accessDetails.accessType === 'admin' || accessDetails.accessType === 'contact'
    }

    // For read operations, any access type is sufficient
    return true
  }
}

/**
 * Factory function to create AccessControlUtil instance
 * Use this in your services to get access control utilities
 */
const createAccessControl = (app: any): AccessControlUtil => {
  return new AccessControlUtil(app)
}

// Export the main hook, helpers, and utility class
export {accessControl, checkRecordAccess, getUserAccessibleSchools, hasSchoolAccess, AccessControlUtil, createAccessControl}
