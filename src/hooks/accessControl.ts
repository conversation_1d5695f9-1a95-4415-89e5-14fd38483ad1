// hooks/access-control.ts

import {HookContext, Hook} from '@feathersjs/feathers'
import {Forbidden, BadRequest} from '@feathersjs/errors'
import hook from '../hook'

interface AccessControlOptions {
  uidField?: string
  isSchoolField?: string
  allowCreate?: boolean
  allowUpdate?: boolean
  adminRoles?: string[]
  managerRoles?: string[]
}

interface User {
  _id?: string
  id?: string
  role?: string
  roles?: string[]
  [key: string]: any
}

interface QueryWithSys {
  $sys?: boolean
  [key: string]: any
}

/**
 * Universal access control hook for uid-based resources (orders, income-logs, etc.)
 * Handles both user and school contexts with admin verification
 * Includes system admin bypass logic
 */
const accessControl = (options: AccessControlOptions = {}): Hook => {
  const {
    uidField = 'uid',
    isSchoolField = 'isSchool',
    allowCreate = false,
    allowUpdate = false,
    adminRoles = ['admin', 'school-admin'],
    managerRoles = ['manager', 'school-manager'],
  } = options

  return async (context: HookContext): Promise<HookContext> => {
    const {app, method, type, params, data, id} = context

    // Only apply to 'before' hooks for find/get and optionally create/update
    if (type !== 'before') return context

    // Apply to find, get, and optionally create/update
    const applicableMethods = ['find', 'get']
    if (allowCreate) applicableMethods.push('create')
    if (allowUpdate) applicableMethods.push('update', 'patch')

    if (!applicableMethods.includes(method)) return context

    // Ensure user is authenticated
    if (!params.user) {
      throw new Forbidden('Authentication required')
    }

    const currentUser = params.user as User
    const currentUserId = currentUser._id

    if (!currentUserId) {
      throw new Forbidden('Authentication required')
    }

    // Handle system admin bypass logic
    if (method === 'find' && params.query) {
      const query = params.query as QueryWithSys

      if (query.$sys) {
        // Check if user has system admin or manager access
        const hasSystemAccess = hook.roleHas(['sys', 'admin'])(context) || hook.managerRoleHas(managerRoles)(context)

        if (!hasSystemAccess) {
          context.result = null
          return context
        }

        // Remove $sys from query and allow full access
        delete query.$sys
        return context
      }
    }

    // Handle different methods with regular access control
    switch (method) {
      case 'find':
        return handleFind(context, currentUserId, uidField, isSchoolField, adminRoles, app)
      case 'get':
        return handleGet(context, currentUserId, uidField, isSchoolField, adminRoles, app)
      case 'create':
        return handleCreate(context, currentUserId, uidField, isSchoolField, adminRoles, app)
      case 'update':
      case 'patch':
        return handleUpdate(context, currentUserId, uidField, isSchoolField, adminRoles, app)
      default:
        return context
    }
  }
}

// Handle find operations
const handleFind = async (
  context: HookContext,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  adminRoles: string[],
  app: any
): Promise<HookContext> => {
  const {params} = context

  // Get user's accessible school IDs
  const accessibleSchoolIds = await getUserAccessibleSchools(currentUserId, adminRoles, app)

  // Build query filter
  const accessFilter = {
    $or: [
      // User's own records
      {[uidField]: currentUserId, [isSchoolField]: {$ne: true}},
      // School records where user is admin
      ...(accessibleSchoolIds.length > 0
        ? [
            {
              [uidField]: {$in: accessibleSchoolIds},
              [isSchoolField]: true,
            },
          ]
        : []),
    ],
  }

  // Merge with existing query
  if (params.query) {
    params.query = {
      ...params.query,
      ...accessFilter,
    }
  } else {
    params.query = accessFilter
  }

  return context
}

// Handle get operations
const handleGet = async (
  context: HookContext,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  adminRoles: string[],
  app: any
): Promise<HookContext> => {
  const {id, service} = context

  try {
    // Get the record first (without restrictions)
    const record = await service._get(id, {provider: null})

    if (!record) {
      throw new BadRequest('Record not found')
    }

    // Check access
    const hasAccess = await checkRecordAccess(record, currentUserId, uidField, isSchoolField, adminRoles, app)

    if (!hasAccess) {
      throw new Forbidden('Access denied to this record')
    }

    return context
  } catch (error: any) {
    if (error.name === 'Forbidden' || error.name === 'BadRequest') {
      throw error
    }
    throw new BadRequest('Unable to verify access')
  }
}

// Handle create operations
const handleCreate = async (
  context: HookContext,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  adminRoles: string[],
  app: any
): Promise<HookContext> => {
  const {data} = context
  const records = Array.isArray(data) ? data : [data]

  for (const record of records) {
    const hasAccess = await checkCreateAccess(record, currentUserId, uidField, isSchoolField, adminRoles, app)

    if (!hasAccess) {
      throw new Forbidden('Access denied to create this record')
    }
  }

  return context
}

// Handle update operations
const handleUpdate = async (
  context: HookContext,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  adminRoles: string[],
  app: any
): Promise<HookContext> => {
  const {id, service} = context

  try {
    // Get existing record
    const existingRecord = await service._get(id, {provider: null})

    const hasAccess = await checkRecordAccess(existingRecord, currentUserId, uidField, isSchoolField, adminRoles, app)

    if (!hasAccess) {
      throw new Forbidden('Access denied to update this record')
    }

    return context
  } catch (error: any) {
    if (error.name === 'Forbidden') {
      throw error
    }
    throw new BadRequest('Unable to verify update access')
  }
}

// Helper: Check if user has access to a specific record
const checkRecordAccess = async (
  record: any,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  adminRoles: string[],
  app: any
): Promise<boolean> => {
  const recordUid = record[uidField]
  const recordIsSchool = record[isSchoolField]

  // If it's a user record, check if it belongs to current user
  if (!recordIsSchool) {
    return recordUid === currentUserId
  }

  // If it's a school record, check if user is admin of that school
  return await hasSchoolAccess(currentUserId, recordUid, adminRoles, app)
}

// Helper: Check create access
const checkCreateAccess = async (
  record: any,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  adminRoles: string[],
  app: any
): Promise<boolean> => {
  const recordUid = record[uidField]
  const recordIsSchool = record[isSchoolField]

  // If creating for user, must be current user
  if (!recordIsSchool) {
    return recordUid === currentUserId
  }

  // If creating for school, user must be admin of that school
  return await hasSchoolAccess(currentUserId, recordUid, adminRoles, app)
}

// Helper: Get all school IDs that user has admin access to
const getUserAccessibleSchools = async (userId: string, adminRoles: string[], app: any): Promise<string[]> => {
  try {
    /**
     * 'school-plan' is the collection that contains all the school list.
     * this will depend on colelctions.
     * category 1: [order, gift-cards], category 2: [income-log, payment-methods] etc.
     * for category 1, we need to check either of
     * 1. if user is admin of school: 'school-user' uid: user._id, status: 2, role: 'admin'
     * 2. or is a subject coordinator of any subject of school: 'subjects' coordinator: user._id, del: false, here uid is school id
     * for category 2, we need to check if user is main contact of the school: 'school-plan' contact: user._id
     */
    return []
  } catch (error) {
    console.error('Error getting user accessible schools:', error)
    return []
  }
}

// Helper: Check if user is admin of specific school
const hasSchoolAccess = async (userId: string, schoolId: string, adminRoles: string[], app: any): Promise<boolean> => {
  try {
    /**
     *
     */
    return true
  } catch (error) {
    console.error('Error checking school admin access:', error)
    return false
  }
}

// Export the main hook and helpers
export {accessControl, checkRecordAccess, getUserAccessibleSchools, hasSchoolAccess}
