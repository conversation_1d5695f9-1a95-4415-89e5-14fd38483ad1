import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import hook from '../../hook'
import logger from '../../logger'
import {ObjectId} from 'bson'
import {NotFound} from '@feathersjs/errors'
import {queueForCommit, TxnParams} from '../../hooks/dbTransactions'

export class Session extends Service {
  app: Application
  selectList: String[]
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
    this.selectList = [
      'pid',
      'parent',
      'sid',
      'image',
      'name',
      'start',
      'end',
      'ended',
      'reg',
      'regDate',
      'regNum',
      'regMax',
      'uid',
      'type',
      'rid',
      'status',
      'cid',
      'school',
      'classId',
      'students',
      'taskType',
      'unitType',
      'sessionType',
      'createdAt',
      'updatedAt',
      'id',
      'zoom.id',
      'block',
      'countdown',
      'guest',
      'discount',
      'category',
      'subjects',
      'childSize',
      'childs._id',
      'childs.sessionType',
      'childs.sid',
      'task._id',
      'task.pages',
      'task.toolCount',
      'task.service',
      'task.curriculum',
      'task.mode',
      'task.grades',
      'task.type',
      'task.uid',
      'task.outline.pd.subjects',
      'task.outline.outline.subjects',
      'task.outline.assess.subjects',
      'toolStat',
      'color',
      'premium',
      'promotion',
      'servicePack',
      'booking',
      'substituteWithin',
      'substituteTeacher',
      'substituteTeacherStatus',
      'substituteTeacherMessage',
      'substituteAdmin',
      'substituteAdminStatus',
      'substituteAdminMessage',
      'substitutePackUser',
      'substituteServicePackSnapshot',
      'substituteServicePackUserSnapshot',
      'substituteSubject',
      'substituteTopic',
      'substitutePush',
      'substituteDuration',
      'substitutePushTime',
      'substitutePushAll',
      'substitutePriorityPush',
      'substituteReminder',
      'substituteMatched',
      'substituteOperateAt',
      'questions',
      'substituteHourlyRate',
    ]
  }

  limitPages(one: any) {
    const end = one.task.pageNum > 10 ? 3 : Math.ceil(one.task.pageNum * 0.3)
    one.pages = one.task.pages = one.task.pages.slice(0, end)
  }
  toInfo(_id: String, extSelect: String[] = []) {
    return this.Model.findById(_id).select([...this.selectList, ...extSelect])
  }
  async ext(one: any, params: Params) {
    await this.extUser(one)
    const {_id: uid}: any = Acan.clone(params.user ?? {})
    if (one.reg) {
      // 检查报名数据，如果是子课件则去父课件查询
      if (one.pid) {
        // need get parent reg list
        const rs: any = await this.Model.findById(one.pid).select(['reg'])
        one.reg = rs.reg
      }
      // 获取当前用户的报名信息
      if (uid && one.reg) {
        one.regData = one.reg.find((v: any) => v._id === uid)
        // 不是发布者 不能查看报名数据
        if (one.uid !== uid) one.reg.length = 0
      }
    }
    if (!one.task) return
    one.pageNum = one.task.pageNum = one.task.pages?.length || 0
    if (!one.task.pages) return
    one.pages = one.task.pages
    // no login
    if (!uid) return this.limitPages(one)
    // self
    if (one.uid === uid) return
    if (hook.roleHas(['admin', 'sys'])({params})) return
    if (!['workshop', 'taskWorkshop'].includes(one.type)) return
    // 没有报名的用户限制查看数量
    if (!one.regData) {
      if (params.method?.includes(one.sid)) return // in classroom not limit
      this.limitPages(one)
    }
  }
  async extService(one: any, params: Params) {
    if (!one.servicePack) return
    one.serviceInfo = await this.app.service('service-pack').Model.findById(one.servicePack._id)
  }
  // 查看预约课的评价记录数量
  async extRating(one: any, params: Params) {
    if (!one.booking) return // 非预约课堂不需要
    const uid = params.user?._id.toString()
    if (one.uid === uid) {
      // 老师身份统计
      one.rating = await this.app.service('service-rating').Model.count({session: one._id, servicer: uid})
    } else {
      // 学生身份统计
      one.rating = await this.app.service('service-rating').Model.count({session: one._id, booker: uid})
    }
  }
  // 查看预约课的标签
  async extBooking(arr: any, params: Params) {
    const $in = arr.filter((v: any) => v.booking).map((v: any) => v.booking)
    const bookList: any = Acan.clone(await this.app.service('service-booking').Model.find({_id: {$in}}).select('packUser'))
    const bookMap: any = {}
    for (const o of bookList) {
      bookMap[o._id] = o.packUser
    }
    const packList = Acan.clone(
      await this.app
        .service('service-pack-user')
        .Model.find({_id: {$in: Object.values(bookMap)}})
        .select('snapshot.subject snapshot.topic snapshot.curriculum snapshot.gradeGroup')
    )
    const packMap: any = {}
    for (const o of packList) {
      packMap[o._id] = o.snapshot
    }
    for (const o of arr) {
      if (o.booking) o.packUser = packMap[bookMap[o.booking]]
    }
  }
  getSessionUrl({_id, type}: any, params: Params) {
    const typeUrl = type.toLowerCase().includes('courses') ? 'course' : 'session'
    // const back = encodeURIComponent(`/com/enroll?tab=${type}#enrolled`)
    const url = `${SiteUrl}/v2/detail/${typeUrl}/${_id}` // ?back=${back}
    return url
  }
  // auto live parent status
  async liveParent({pid, sessionType}: any, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    let con = {_id: pid, type: {$in: ['courses', 'pdCourses', 'unitCourses']}}
    let needActive = true
    if (sessionType !== 'live') {
      const pbj: any = await this.Model.findById(pid, null, options).select(['status', 'pid', 'sessionType'])
      if (pbj.status !== 'close' || pbj.sessionType === 'live') {
        needActive = false
      } else if (pbj.pid) {
        const tbj: any = await this.Model.findById(pbj.pid, null, options).select(['status', 'pid', 'sessionType'])
        if (tbj.sessionType === 'live') needActive = false
      }
    }
    if (needActive) this.Model.updateOne(con, {$set: {status: 'live'}}, options).then()
  }
  // auto end parent status
  async endParent(pid: String, params?: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const pdoc: any = await this.Model.findById(pid, null, options).select(['status', 'type', 'sessionType', 'childs'])
    if (!['unitCourses', 'courses', 'pdCourses'].includes(pdoc.type)) return logger.warn(`endParent: parent not courses!`)
    const $in = pdoc.childs.map((v: any) => v._id) //.filter((v: any) => pdoc.sessionType === 'student' || v.type !== 'tool')
    const childs: any = await this.Model.find({$or: [{_id: {$in}}, {pid: {$in}}]}, null, options).select(['sessionType', 'status']) // , type: {$ne: 'tool'}
    const liveArr = childs.filter((v: any) => v.sessionType === 'live')
    let arr = []
    if (!Acan.isEmpty(liveArr)) {
      // only count live
      arr = liveArr.filter((v: any) => v.status !== 'close')
    } else {
      // count all
      arr = childs.filter((v: any) => v.status !== 'close')
    }
    logger.warn('endParent:', pdoc.type, pdoc.status, arr, pid)
    if (!Acan.isEmpty(arr) && pdoc.status === 'close') await this.Model.updateOne({_id: pid}, {$set: {status: 'live'}}, options)
    else if (Acan.isEmpty(arr) && pdoc.stutus !== 'close') await this.Model.updateOne({_id: pid}, {$set: {ended: new Date(), status: 'close'}}, options)
  }
  async sendEndNotice(result: any, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const {_id, name, uid, type, booking} = result
    let bookData: any = await this.app.service('service-booking').Model.findById(booking, null, options)
    const servicer = await this.app.service('users').uidToInfo(bookData.servicer)
    const booker = await this.app.service('users').uidToInfo(bookData.booker)
    const url = this.getSessionUrl({_id, type}, params)
    await queueForCommit(
      this.app,
      'notice-tpl',
      'mailto',
      ['ReminderWhenMentorSessionEnds', servicer.email, {username: servicer.nickname, session_name: name, url}],
      params
    )
    const url2 = `${SiteUrl}/v2/detail/session/${_id}?evaluation=true`
    await queueForCommit(
      this.app,
      'notice-tpl',
      'mailto',
      ['ReminderOfEvaluatingTheSession', booker.email, {username: booker.nickname, session_name: name, url: url2}],
      params
    )
  }
  async regNotice(data: any, params: TxnParams, result: any, reg: any) {
    if (!result) return logger.error('regNotice not result')
    const {_id, name, uid, start, type} = result
    const user = await this.app.service('users').uidToInfo(uid)
    logger.warn('send reg mail:', {name, author: user.nickname, date: data._date || start})
    const url = this.getSessionUrl({_id, type}, params)

    await queueForCommit(
      this.app,
      'notice-tpl',
      'mailto',
      ['WorkshopReg', params.user, {username: reg.nickname, name, url, author: user.nickname, date: data._date || start}],
      params
    )
  }
  // async removeNotice({reg, name, start, uid}: any, params: Params) {
  //   if (params.inside) {
  //     let user = await this.app.service('users').uidToInfo(uid)
  //     this.app.service('notice-tpl').send('WorkshopDeleteCron', user, {name, start, message: params.query?.message || ''})
  //   }

  //   const userIds = reg.map((v: any) => v._id)
  //   if (Acan.isEmpty(userIds)) return console.warn('not reg user', reg)
  //   const users = await this.app
  //     .service('users')
  //     .Model.find({_id: {$in: userIds}})
  //     .select(['email'])
  //   if (Acan.isEmpty(users)) return console.warn('not found user', userIds)
  //   users.map(async (o: any) => {
  //     this.app.service('notice-tpl').send(params.inside ? 'WorkshopDeleteCron' : 'WorkshopDelete', o, {name, start, message: params.query?.message || ''})
  //   })
  // }
  async removeNotice(doc: any, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    let {reg, students, name, start, end, uid, school} = doc
    // 通知老师
    this.sendCancelNotice({doc}, params)

    const regIds = reg.map((v: any) => v._id)
    const studentIds = students || []
    const userIds = regIds.concat(studentIds)
    if (Acan.isEmpty(userIds)) return console.warn('not reg user', reg)
    const users = await this.app
      .service('users')
      .Model.find({_id: {$in: userIds}}, null, options)
      .select(['_id', 'email', 'name'])
    if (Acan.isEmpty(users)) return console.warn('not found user', userIds)

    // 通知学生
    for (let i = 0; i < users.length; i++) {
      const user: any = users[i]
      let startTime = new Date(start)
      let endTime = new Date(end)
      let option: any = {timeZone: user.timeZone || 'Asia/Shanghai', timeZoneName: 'short', hour12: false}
      let time = `${startTime.toLocaleString('en', option)} - ${endTime.toLocaleString('en', option)}`

      let username = user.name.join(' ')
      let schoolname = ''
      if (school) {
        let schoolPlan: any = await this.app.service('school-plan').Model.findOne({_id: school}, null, options)
        if (schoolPlan && !schoolPlan.personal) {
          schoolname = `under ${schoolPlan.name}`
          let student: any = await this.app.service('students').Model.findOne({uid: user._id, school: school}, null, options)
          username = student?.name.join(' ')
        }
      }
      queueForCommit(
        this.app,
        'notice-tpl',
        'send',
        ['CancellationOfTheSession', user, {name, username, time, schoolname, message: params.query?.message || ''}],
        params
      )
      // this.app.service('notice-tpl').send('CancellationOfTheSession', user, {name, username, time, schoolname, message: params.query?.message || ''})
    }
  }
  async removeRelated(one: any, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const {_id, pid, childs} = Acan.clone(one)
    // remove childs
    if (!Acan.isEmpty(childs)) {
      const $in = childs.map((v: any) => v._id)
      this.Model.deleteMany({_id: {$in}}, options).then()
    }
    // remove parent.childs
    if (pid) {
      const pdoc = Acan.clone(await this.Model.findById(pid, null, options).select(['childs', 'type']))
      if (!pdoc) return logger.log('removeRelated: pid not find', pid)
      const index = pdoc.childs.findIndex((v: any) => v._id === _id)
      if (index === -1) return
      if (pdoc.childs.length === 1 && !['session', 'workshop', 'taskWorkshop'].includes(pdoc.type)) {
        this.Model.deleteOne({_id: pid}, options).then()
      } else {
        this.Model.updateOne({_id: pid}, {$set: {childSize: pdoc.childSize - 1}, $pull: {childs: {_id}}}, options).then()
      }
    }
  }
  async getCalendarList(query: any, params: Params) {
    const {zone = 0, start, end, type, school, classId, $or} = params.query ?? {}
    const nd = new Date()
    const nt = nd.getTime()
    const todayT = nt - (nt % 86400000) // + zone * 60000
    const di = nd.getDay()
    const defDay = [todayT - (di === 0 ? 6 : di - 1) * 86400000, todayT + (di === 0 ? 1 : 8 - di) * 86400000]
    const con: any = {del: false, sessionType: 'live', type, school, classId, $or}
    con.end = {$gt: utcDate(start || defDay[0], zone)}
    con.start = {$lt: utcDate(end || defDay[1], zone)}
    if (!school && !classId && !$or) {
      con.uid = params.user?._id
    }
    Acan.objClean(con)
    logger.warn(con)
    const rs = await this.Model.find(con).select(this.selectList)
    const list = Acan.clone(rs)
    for (const one of list) {
      await this.extUser(one)
    }
    return list
  }
  async getDateList(query: any, params: Params) {
    const {_id} = params.user ?? {}
    const {zone = 0, start, end, school, status, classId} = params.query ?? {}
    const con: any = {del: false, end: {$gt: utcDate(`${start}`, zone)}, start: {$lt: utcDate(`${end}`, zone)}}
    con.$or = []
    if (!status || status.includes('3')) con.$or.push({uid: _id})
    if (!status || status.includes('4')) con.$or.push({'reg._id': _id})
    if (status) con.type = 'workshop'
    if (Acan.isEmpty(con.$or)) delete con.$or
    if (!Acan.isEmpty(classId)) {
      delete con.$or
      con.classId = classId
    } else if (!Acan.isEmpty(school)) {
      con.school = Array.isArray(school) ? {$in: school.map((v) => (v === '0' ? null : v))} : school
    }
    logger.warn('start', utcDate(`${start}`, zone), con)
    const rs = await this.Model.find(con).select(this.selectList)
    const list = Acan.clone(rs)
    for (const one of list) {
      await this.extUser(one)
    }
    return list
  }
  async getStat(query: any, params: Params) {
    const rs: any = {}
    rs.type = await this.Model.aggregate([
      {
        $group: {_id: '$type', count: {$sum: 1}},
      },
    ])
    rs.status = await this.Model.aggregate([
      {
        $group: {_id: '$status', count: {$sum: 1}},
      },
    ])
    return rs
  }
  async getReg(get: any, params: Params) {
    const rs: any = await this.Model.findById(get._id).select(['reg'])
    if (!rs?.reg) return {}
    // const emailList = await this.app.service('users').emailList(rs.reg.map((v: any) => v._id))
    // rs.reg.map((v: any) => {
    //   v.email = emailList[v._id]
    // })
    // logger.warn('getReg:', rs, get._id)
    return rs
  }
  async getUnReg({_id}: any, params: Params) {
    const uid = params.user?._id
    const rs: any = await this.Model.findById(_id).select('reg')
    if (!rs?.reg) return {n: 0}
    // find self and pull
    const reg = rs.reg.find((v: any) => v._id === uid)
    if (!reg) {
      return {n: 0}
    }
    const regNum = rs.reg.length - 1
    return await this.Model.updateOne({_id}, {$pull: {reg}, $set: {regNum}})
  }
  async getCheck({sid}: any, params: Params) {
    return await this.Model.findOne({sid}).select(['sid'])
  }
  async getPull({_id}: any, params: Params) {
    // pull data from new db
    if (!_id) return {message: 'query error'}
    const one = Acan.clone(await this.Model.findById(_id).select(['id', 'rev', 'sid', 'cid', 'type']))
    await this.extSnapshot(one, true)
    if (one.cid) {
      const task = await this.app.service('unit').snapshot({_id: one.cid}, params)
      one.task = task
      await this.Model.updateOne({_id}, {$set: {task}})
    }
    // await this.extSlides(one, true)
    return one
  }
  async getLast(get: any, params: Params) {
    const {_id} = params.user ?? {}
    logger.warn('session getLast', _id)
    if (!_id) return {message: 'no login'}
    return await this.app.get('redis').HGET('SessionLastSid', _id)
  }
  async extUser(one: any) {
    if (!one.uid) return
    one.owner = await this.app.service('users').uidToInfo(one.uid)
    if (one.school) {
      one.schoolInfo = await this.app.service('school-plan').getInfo({school: one.school})
      one.owner = await this.app.service('school-user').getInfo({school: one.school, email: one.owner.email})
      // Object.assign(one.owner, rs)
    }
    if (one.substituteTeacher) {
      if (one.substituteWithin) {
        one.substituteTeacherInfo = await this.app.service('school-user').Model.findOne({uid: one.substituteTeacher, school: one.school})
      } else {
        one.substituteTeacherInfo = await this.app.service('users').uidToInfo(one.substituteTeacher)
      }
    }
    // logger.info('uidToInfo', one.school, one.uid, one.owner)
  }
  async getPagesBySid(sid: string) {
    return await this.Model.findOne({sid}).sort({updatedAt: -1}).select('pages')
  }
  async extMaterials(one: any, pull: boolean = false) {
    if (one.materials && !pull) return logger.log(one._id, 'has materials')
    if (!one.id) return logger.log(one.sid, 'has no slides.id')
    let list = await this.app.service('materials').Model.find({id: one.id}).select(['page', 'list'])
    one.materials = Acan.clone(list)
    logger.info(one.sid, ' ext materials:', list.length)
  }
  async extQuestions(one: any, pull: boolean = false) {
    if (one?.video?.videoId) {
      if (one.questions && !pull) return logger.log(one._id, 'has questions')
      const questionsService = this.app.service('questions')
      let list = await questionsService.Model.find({id: one.video.videoId})
      one.questions = Acan.clone(list)
    } else {
      if (one.questions && !pull) return logger.log(one._id, 'has questions')
      if (!one.id) return logger.log(one.sid, 'has no slides.id')
      // 快照完整的互动题数据
      const questionsService = this.app.service('questions')
      let list = await questionsService.Model.find({id: one.id}) //.select(questionsService.questionSelect)
      one.questions = Acan.clone(list)
      logger.info(one.sid, ' ext questions:', list.length)
    }
  }
  async extSnapshot(one: any, pull: boolean = false) {
    await this.extMaterials(one, pull)
    await this.extQuestions(one, pull)
    this.Model.updateOne({_id: one._id}, {$set: {materials: one.materials, questions: one.questions}}).then()
  }
  async extSlides(one: any, pull: boolean = false) {
    if (one.pages && !pull) return logger.log(one._id, 'has pages')
    if (!one.id) return // logger.log(one)
    let rs: any = await this.app.service('slides').Model.findOne({id: one.id})
    if (!rs) return logger.log('no slides', one.sid, one.id)
    const $set = {pageNum: rs.pages?.length, pages: rs.pages, rev: rs.rev}
    Object.assign(one, $set)
    this.app.service('session').Model.updateOne({_id: one._id}, {$set}).then()
  }
  async endAutoClose(arr: any) {
    for (const one of Acan.clone(arr)) {
      await this.patch(one._id, {status: 'close'}, {inside: true})
      // 自动生成 takeaway 数据 #4352
      let res = await this.app.service('session-snapshot').getSnapshot({_id: one._id, createBy: 'cron'}, {})
      this.app.service('service-pack-apply').updateTakeaway({session: one._id, snapshot: res})
    }
    return {ok: arr.length}
  }
  // workshop的排课 结束时间到了自动结束课堂
  // https://github.com/zran-nz/bug/issues/5405
  async workshopEndAutoClose() {
    const arr = await this.Model.find({pid: {$exists: false}, type: {$in: Agl.workshopTypes}, status: {$ne: 'close'}, end: {$lt: new Date()}}).limit(1000)
    await this.endAutoClose(arr)
  }
  // 预约的排课 结束时间到了自动结束课堂
  async getBookingEndAutoClose() {
    const arr = await this.Model.find({uid: {$ne: null}, booking: {$ne: null}, status: {$ne: 'close'}, end: {$lt: new Date()}}).limit(1000)
    await this.endAutoClose(arr)
  }
  // 最小报名人数未到, 自动删除session
  async getCronAutoClose({}: any) {
    const regDate = {$gte: new Date(Date.now() - 86400000 * 14), $lte: new Date()} // regDate has expire , -7d < regDate <= now
    const con = {del: false, status: {$ne: 'close'}, pid: {$exists: false}, regDate, type: {$in: Agl.workshopTypes}}
    const rs: any = await this.Model.find({...con, $expr: {$gt: ['$discount.size', '$regNum']}}).select([
      'name',
      'status',
      'type',
      'discount.size',
      'childs',
      'regNum',
      'start',
      'end',
    ])
    for (const one of rs) {
      await this.app.service('order').getCancelByLinkId({linkId: one._id, status: 502}, {})
      this.remove(one._id, {inside: true}).then()
      this.app.service('log').create({type: 'node.session.getCronAutoClose', ip: global.LocalIp, body: one._id, msg: 'remove session'}).then()
      if (one.childs) {
        for (const co of one.childs) {
          await this.app.service('order').getCancelByLinkId({linkId: co._id, status: 502}, {})
          this.remove(co._id, {inside: true}).then()
          this.app
            .service('log')
            .create({
              type: 'node.session.getCronAutoClose',
              ip: global.LocalIp,
              body: {
                pid: one._id,
                _id: co._id,
              },
              msg: 'remove session childs',
            })
            .then()
        }
      }
    }
    return {rs, con}
  }
  async cron1({}: any, params?: Params): Promise<any> {
    this.getBookingEndAutoClose()
    this.workshopEndAutoClose()
    this.getCronAutoClose({})
    this.reminderDeadline()
    this.mentorAutoRate()
    this.substitute3HoursCancel(params || {})
    this.substitutePush(params)
    return await this.reminderBeforeClass()
  }
  // 开课前2小时提醒
  async reminderBeforeClass() {
    const select = ['_id', 'name', 'sid', 'start', 'type', 'uid', 'school', 'students', 'reg', 'substituteTeacher', 'substituteTeacherStatus', 'booking']
    const $lte = new Date(Date.now() + 60 * 60 * 1000)
    const $gte = new Date(Date.now() + 2 * 60 * 1000)
    // start time between 1 ~ 24h
    // not del, notice
    const list: any = Acan.clone(
      await this.Model.find({del: false, sessionType: 'live', reminder: 0, status: {$ne: 'close'}, start: {$gte, $lte}}).select(select)
    )
    const toStudent = async (uid: any, post: any, one: any) => {
      const user = await this.app.service('users').uidToInfo(uid)
      if (!user) return
      const startTime = new Date(one.start).toLocaleString(user.lang || 'en-US', {timeZone: user.timeZone || 'UTC', timeZoneName: 'short'})
      post.startTime = startTime
      await this.app.service('notice-tpl').mailto('WorkshopReminderBeforeClass', user, post)

      // 预约课程 短信提醒学生
      if (one.booking) {
        const url = this.getSessionUrl({_id: one._id, type: one.type}, {})
        await this.app.service('notice-tpl').mailto('Reminder2HoursBeforeMentorServiceSessionBegins', user, {...post, username: user.name.join(' '), url})
      }
    }
    for (const one of list) {
      const {lang, timeZone} = (one.user = await this.app.service('users').uidToInfo(one.uid))
      const url = this.getSessionUrl(one, {})
      // const exTime = Math.floor((new Date(one.start).getTime() - Date.now()) / 1000)
      // const timeLeft = '{hour} hours and {min} minutes'.replace('{hour}', Math.floor(exTime / 3600) + '').replace('{min}', Math.floor(exTime / 3600) + '')
      const startTime = new Date(one.start).toLocaleString(lang || 'en-US', {timeZone: timeZone || 'UTC', timeZoneName: 'short'})
      const post = {type: one.type, name: one.name, start: one.start, startTime, url}
      await this.app.service('notice-tpl').mailto('WorkshopReminderBeforeClass', one.user, post)
      // 提醒学生
      if (!Acan.isEmpty(one.reg)) {
        for (const reg of one.reg) await toStudent(reg._id, post, one)
      } else {
        for (const uid of one.students) await toStudent(uid, post, one)
      }
      // 提醒代课老师
      if (one.substituteTeacher && one.substituteTeacherStatus == 1) {
        let sendUser = await this.app.service('users').uidToInfo(one.substituteTeacher)
        await this.app.service('notice-tpl').mailto('WorkshopReminderBeforeClass', sendUser, post)
      }
      // arr.push(rs)
      // if (rs.response.includes('250'))
      await this.app.service('session').Model.updateOne({_id: one._id}, {$set: {reminder: 1}})
    }
    return list
  }
  // 截止时间前15分钟提醒学生
  async reminderDeadline() {
    const select = ['_id', 'name', 'sid', 'start', 'type', 'uid', 'school', 'students', 'reg']
    const $lte = new Date(Date.now() + 15 * 60 * 1000)
    const $gte = new Date(Date.now() + 1 * 60 * 1000)
    const list: any = Acan.clone(
      await this.Model.find({del: false, reminder: {$in: [0, 1]}, status: {$ne: 'close'}, 'countdown.deadline': {$gte, $lte}}).select(select)
    )
    const toStudent = async (uid: any, post: any) => {
      const user = await this.app.service('users').uidToInfo(uid)
      if (!user) return
      await this.app.service('notice-tpl').mailto('ReminderDeadlineToStudent', user, post)
    }
    for (const one of list) {
      const url = this.getSessionUrl(one, {})
      const post = {type: one.type, name: one.name, url}
      // 提醒学生
      if (!Acan.isEmpty(one.reg)) {
        for (const reg of one.reg) await toStudent(reg._id, post)
      } else {
        for (const uid of one.students) await toStudent(uid, post)
      }
      await this.app.service('session').Model.updateOne({_id: one._id}, {$set: {reminder: 2}})
    }
    return list
  }
  // for tool members
  async getToolMembers({sid, role = 'teacher', isChild = false}: any, params: Params): Promise<any> {
    const con = isChild ? {'childs.sid': sid} : {sid}
    let rs: any = Acan.clone(await this.Model.findOne(con).select(['students', 'reg', 'type']))
    if (!rs) return []
    let mrr: any, arr: any
    if (!Acan.isEmpty(rs.students)) {
      mrr = rs.students
    } else {
      mrr = rs.reg.map((v: any) => v._id)
    }
    if (Acan.isEmpty(mrr)) return await this.getToolMembers({sid, role, isChild: true}, params)
    if (role === 'student') {
      const i = mrr.findIndex((m: any) => {
        return m === params.user?._id
      })
      arr = mrr.slice(i, i + 3)
      if (arr.length < 3 && i > 0) arr.push(...mrr.slice(0, i))
      if (arr.length > 3) arr.length = 3
      logger.warn(arr, mrr)
    } else {
      arr = mrr
    }
    const trr = []
    let uid
    for (const _id of arr) {
      uid = _id
      if (!Acan.isObjectId(uid)) uid = await this.app.service('users').uidToId(uid)
      trr.push(await this.app.service('users').uidToInfo(uid))
    }
    return trr
  }
  async getSessions({ids}: any, params: Params) {
    const sessionSelect = ['sid', 'uid', 'name', 'start', 'end', 'countdown', 'type', 'sessionType', 'status', 'questions', 'task.toolCount']
    const sessions: any = []
    const slist = Acan.clone(await this.Model.find({_id: {$in: ids}}).select(sessionSelect))
    slist.map((v: any) => {
      const qrr = v.questions?.filter((q: any) => q.type !== 'website') ?? []
      const pages = qrr.map((q: any) => q.page)
      const o = Acan.clone(v)
      delete o.questions
      sessions.push({...o, total: qrr.length, pages})
    })
    return sessions
  }
  async getStudentLearningData({uid, ids}: any, params: Params): Promise<any> {
    const sessions = await this.getSessions(ids, params)
    const sids = sessions.map((v: any) => v.sid)
    const responses = Acan.clone(await this.Model.find({uid, sid: {$in: sids}}).select(['page', 'sid', 'uid', 'type']))
    const rs: any = {}
    for (const s of sessions) {
      const rbj: any = {}
      responses.map((v: any) => {
        if (v.sid !== s.sid || !s.pages.includes(v.page)) return
        rbj[v.page] = 1
      })
      rs[s._id] = Object.keys(rbj).length
    }
    return {sessions, count: rs}
  }
  async getLearningData({pid, role = 'teacher'}: any, params: Params) {
    const doc = Acan.clone(await this.Model.findById(pid).select(['sid', 'childs', 'type']))
    const members = await this.getToolMembers({sid: doc.sid, role}, params)
    if (!doc || Acan.isEmpty(doc.childs)) return {members, sessions: []}
    const ids = doc.childs?.map((v: any) => v._id)
    if (['session', 'workshop', 'taskWorkshop'].includes(doc.type)) ids.push(pid)
    const sessions = await this.getSessions({ids}, params)
    const sids = sessions.map((v: any) => v.sid)

    const toolDataSelect = ['tool', 'session', 'assessor', 'student']
    const toolDatas = Acan.clone(
      await this.app
        .service('tool-data')
        .Model.find({filled: true, session: {$in: sids}})
        .select(toolDataSelect)
    )
    const responses = Acan.clone(
      await this.app
        .service('response')
        .Model.find({sid: {$in: sids}})
        .select(['page', 'sid', 'uid', 'type'])
    )
    const attends = Acan.clone(
      await this.app
        .service('rooms')
        .Model.find({sid: {$in: sids}})
        .select(['sid', 'attend'])
    )
    for (const member of members) {
      member.sessions = {}
      for (const s of sessions) {
        if (s.type === 'tool') {
          const o = {self: 0, teacher: 0, others: 0}
          for (const v of toolDatas) {
            if (v.session !== s.sid) continue
            if (v.student !== member._id) continue
            if (v.assessor === member._id) o.self += 1
            else if (v.assessor === 'teacher') o.teacher += 1
            else o.others += 1
          }
          member.sessions[s._id] = o
          console.log(member.name, s._id, o)
        } else if (s.sessionType === 'live') {
          for (const v of attends) {
            if (v.sid !== s.sid) continue
            member.sessions[s._id] = v.attend.includes(member._id)
          }
        } else {
          const rbj: any = {}
          for (const v of responses) {
            if (v.uid !== member._id || v.sid !== s.sid || !s.pages.includes(v.page)) continue
            rbj[v.page] = 1
          }
          member.sessions[s._id] = {count: Object.keys(rbj).length}
        }
      }
    }
    return {members, sessions, toolDatas}
  }
  async recommend(con: any, params: Params) {
    const {$skip = 0, $limit = 10} = con
    logger.warn('recommend:', con, $skip, $limit)
    const rs = Acan.clone(await this.Model.find(con).select(this.selectList).skip($skip).limit($limit).sort({start: 1}))
    for (const o of rs) {
      await this.extUser(o)
    }
    return rs
  }
  userCon(con: any, params: Params) {
    const uid = params.user?._id
    if (hook.roleHas(['student'])({params})) {
      return {$or: [{'reg._id': uid}, {students: uid}]}
    } else {
      if (hook.roleHas(['sys'])({params}) && con.$sys) return {}
      return {uid}
    }
  }
  recommendLive(con: any, params: Params) {
    if (con.name?.$search) con.name = new RegExp(con.name.$search, 'g')
    // const type = {$in: ['session', 'workshop', 'taskWorkshop']}
    const status = {$ne: 'close'}
    return {status, sessionType: 'live', ...con}
  }
  recommendStudent(con: any, params: Params) {
    if (con.name?.$search) con.name = new RegExp(con.name.$search, 'g')
    // const type = {$in: ['session', 'workshop', 'taskWorkshop', 'tool']}
    const status = {$ne: 'close'}
    return {status, sessionType: 'student', ...con}
  }
  async recommendList(con: any, params: Params) {
    this.queryDate(con, params)
    // console.warn('recommendList', con)
    const data = await this.recommend(con, params)
    return {total: await this.Model.count(con), limit: parseInt(con.$limit || 10), skip: parseInt(con.$skip || 0), data}
  }
  // limit firstday data
  limitOneDay(arr: any) {
    const trr = []
    let firstDay = null
    for (const o of arr) {
      const dt = new Date(o.start)
      const day = `${dt.getFullYear()}-${dt.getMonth()}-${dt.getDate()}`
      if (firstDay === null) firstDay = day
      if (firstDay === day) trr.push(o)
    }
    return trr
  }
  queryDate(query: any, params: Params) {
    const [start, end, zone = 0] = query.dateRange || []
    if (start && end) {
      // live:    start-- query.start -end----start- query.end -- end
      // student: query.start -start----start- query.end
      query.start = Object.assign(query.start || {}, {$lte: utcDate(`${end}`, zone)})
      if (query.sessionType === 'student') {
        delete query.end
        if (!query.start.$gt && !query.start.$gte) query.start.$gte = utcDate(`${start}`, zone)
      } else {
        query.end = {$gte: utcDate(`${start}`, zone)}
      }
      delete query.dateRange
    }
    const statusList = ['scheduled', 'ongoing', 'ended']
    if (statusList.includes(query.status)) {
      if (query.status === 'ended') {
        query.status = 'close'
      } else {
        if (query.status === 'ongoing') {
          query.start = {...query.start, $lte: new Date()}
        } else {
          query.start = {...query.start, $gte: new Date()}
        }
        query.status = {$ne: 'close'}
      }
    }
    logger.warn('queryDate:', query.start, query.end)
  }
  // 首页推荐内容
  async getRecommend(con: any, params: Params) {
    const rs: any = {live: [], student: [], myLive: [], myStudent: [], booking: [], service: []}
    // con.start = {$gte: new Date()}
    const uid = params.user?._id
    con = con || {}
    con.type = {$in: [...Agl.sessionTypeStudentSession]}
    // 可预约的列表 3天内即将过期，最近过期的排前
    const nd = new Date()
    const toBeBookedquery = {expireSoon: {$gt: nd, $lt: new Date(nd.getTime() + 3 * 86400000)}, $sort: {expireSoon: 1}, $limit: 10, $skip: 0}
    let list: any = await this.app.service('service-pack-user').find({...params, query: toBeBookedquery})
    rs.toBeBooked = list.data
    const toBeScheduledQuery = {cancel: null, $limit: 10, $skip: 0, session: null, tab: 'servicer'}
    if (!hook.roleHas(['student'])({params})) {
      con.type.$in.push(...Agl.sessionTypeTeacherSession)
      const userCon = {uid}
      rs.live = this.limitOneDay(await this.recommend(this.recommendLive({...con, ...userCon, end: {$gte: new Date()}}, params), params))
      rs.student = this.limitOneDay(await this.recommend(this.recommendStudent({...con, ...userCon, 'countdown.deadline': {$gte: new Date()}}, params), params))
      // 待排课列表
      list = await this.app.service('service-booking').find({...params, query: toBeScheduledQuery})
      rs.toBeScheduled = list.data
    }
    const userCon = {$or: [{'reg._id': uid}, {students: uid}]}
    // 我的live课
    rs.myLive = this.limitOneDay(await this.recommend(this.recommendLive({...con, ...userCon, end: {$gte: new Date()}}, params), params))
    // 我的自学习课
    rs.myStudent = this.limitOneDay(await this.recommend(this.recommendStudent({...con, ...userCon, 'countdown.deadline': {$gte: new Date()}}, params), params))
    if (isDev)
      rs.query = {
        toBeBooked: {service: 'service-pack-user', query: toBeBookedquery},
        toBeScheduled: {service: 'service-booking', query: toBeScheduledQuery},
      }
    return rs
  }
  // as a teacher
  async getRecommendLive(con: any, params: Params) {
    const uid = params.user?._id
    const type = {$in: [...Agl.sessionTypeStudentSession, ...Agl.sessionTypeTeacherSession]}
    if (!con.type) con.type = type
    con = {type, ...con, uid}
    return this.recommendList(this.recommendLive(con, params), params)
  }
  async getRecommendStudent(con: any, params: Params) {
    const uid = params.user?._id
    const type = {$in: [...Agl.sessionTypeStudentSession, ...Agl.sessionTypeTeacherSession]}
    con = {type, ...con, uid}
    return this.recommendList(this.recommendStudent(con, params), params)
  }
  // as a student
  getRecommendMyLive(con: any, params: Params) {
    const uid = params.user?._id
    const type = {$in: [...Agl.sessionTypeStudentSession]}
    con = {type, ...con, $or: [{'reg._id': uid}, {students: uid}]}
    return this.recommendList(this.recommendLive(con, params), params)
  }
  getRecommendMyStudent(con: any, params: Params) {
    const uid = params.user?._id
    const type = {$in: [...Agl.sessionTypeStudentSession]}
    con = {type, ...con, $or: [{'reg._id': uid}, {students: uid}]}
    return this.recommendList(this.recommendStudent(con, params), params)
  }
  // library 首页
  // student:  下的自学习课件
  // teacher: 发布的系列课，课件，公开课，
  async getIndex({$limit = 3}: any, params: Params) {
    // if (hook.roleHas(['student'])({params})) {
    //   const study = await this.getSearch({type: 'selfStudy', $limit}, params)
    //   const live = await this.getSearch({type: {$in: [...Agl.sessionTypeStudentSession, ...Agl.sessionTypeStudentCourses]}, premium: false, $limit}, params)
    //   const premium = await this.getSearch({type: {$in: [...Agl.sessionTypeStudentSession, ...Agl.sessionTypeStudentCourses]}, premium: true, $limit}, params)
    //   return {study, live, premium}
    // } else {
    // const study = await this.getSearch({type: 'taskWorkshop', sessionType: 'student', $limit}, params)
    const taskWorkshop = await this.getSearch({type: 'taskWorkshop', sessionType: 'live', $limit}, params)
    const unitCourses = await this.getSearch({type: 'unitCourses', $limit}, params)
    return {unitCourses, taskWorkshop}
    // }
  }
  async getIndexStudent({$limit = 3}: any, params: Params) {
    const study = await this.getSearch({type: 'selfStudy', $limit}, params)
    const live = await this.getSearch({type: {$in: [...Agl.sessionTypeStudentSession, ...Agl.sessionTypeStudentCourses]}, premium: false, $limit}, params)
    const premium = await this.getSearch({type: {$in: [...Agl.sessionTypeStudentSession, ...Agl.sessionTypeStudentCourses]}, premium: true, $limit}, params)
    return {study, live, premium}
  }
  // 自学习课
  getIndexStudy(con: any, params: Params) {
    return this.indexList({...con, type: 'selfStudy'}, params)
  }
  // 普通课
  getIndexLive(con: any, params: Params) {
    return this.indexList(
      {...con, type: {$in: [...Agl.sessionTypeStudentSession, ...Agl.sessionTypeStudentCourses]}, sessionType: 'live', premium: false},
      params
    )
  }
  // 精品课
  getIndexPremium(con: any, params: Params) {
    return this.indexList(
      {...con, type: {$in: [...Agl.sessionTypeStudentSession, ...Agl.sessionTypeStudentCourses]}, sessionType: 'live', premium: true},
      params
    )
  }
  // 推广课
  async getIndexPromotion(con: any, params: Params) {
    Object.assign(con, {sessionType: 'live', promotion: true})
    const subjectCodeMap = this.app.get('pdSubjectCodeMap')
    const studentTypes = []
    const teacherTypes = []
    for (const code in subjectCodeMap) {
      if (subjectCodeMap[code].participants === 'students') studentTypes.push(code)
      else teacherTypes.push(code)
    }
    if (hook.roleHas(['student'])({params})) {
      con['task.service.type'] = {$in: studentTypes}
    } else {
      con['task.service.type'] = {$in: teacherTypes}
    }
    // 排除自己报名的数据
    if (params.user?._id) {
      con['reg._id'] = {$ne: params.user?._id}
    }
    return this.indexList(con, params)
  }
  indexQuery(con: any) {
    Object.assign(con, {del: false, status: {$ne: 'close'}, school: null, classId: null, regDate: {$gt: new Date()}, pid: {$exists: false}})
  }
  async indexList(con: any, params: Params) {
    this.queryDate(con, params)
    this.indexQuery(con)
    const data = await this.indexFind(con, params)
    const rs: any = {total: await this.Model.count(con), limit: parseInt(con.$limit || 10), skip: parseInt(con.$skip || 0), data}
    if (isDev) rs.query = con
    return rs
  }
  async indexFind(con: any, params: Params) {
    if (con.sessionType === 'student' || con.type === 'selfStudy') delete con.regDate
    if (con.key) {
      con.name = {$regex: con.key.trim(), $options: 'i'}
      delete con.key
    }
    const sessionFind = this.app
      .service('session')
      .Model.find(con)
      .sort(con.$sort ? con.$sort : {updatedAt: -1})
      .select(this.selectList)
    const data = Acan.clone(await sessionFind.skip(con.$skip || 0).limit(con.$limit || 10))
    for (const one of data) {
      await this.ext(one, params)
      await this.extService(one, params)
    }
    return data
  }
  getSearch(con: any, params: Params) {
    this.indexQuery(con)
    return this.indexFind(con, params)
  }
  async getRoaster({sid}: any, params: Params) {
    const {type, school, reg, students}: any = (await this.Model.findOne({sid}).select(['school', 'type', 'reg', 'students'])) || {}
    if (!type) return Promise.reject(new NotFound())
    const $in = !Acan.isEmpty(students) ? students : !Acan.isEmpty(reg) ? reg.map((v: any) => v._id) : []
    const list: any = []
    if (Acan.isEmpty($in)) return list
    if (school) {
      const model = Agl.sessionTypeStudentSession.includes(type) ? 'students' : 'school-user'
      for (const id of $in) {
        const doc = await this.app.service('users').uidToInfo(id)
        if (!doc) {
          logger.warn('no found user:', id, 'school: ', school, 'length: ', $in.length)
          continue
        }
        let member = {}
        // 查询学校下的用户数据
        if (doc.email) {
          const rs = await this.app.service(model).getInfo({school, email: doc.email})
          if (rs) member = rs
        }
        list.push({...member, uid: id})
      }
    } else {
      for (const id of $in) {
        list.push(await this.app.service('users').uidToInfo(id))
      }
    }
    return list
  }
  // uid = {$in: [...]} or 'xxx'
  async getHours({uid, end, start}: any) {
    start = {$gte: start || new Date()}
    const query: any = {
      $or: [{'reg._id': uid, school: null}, {uid}, {substituteTeacher: uid, substituteTeacherStatus: 1}],
      start,
      sessionType: 'live',
      booking: null,
    }
    if (end) query.start.$lte = new Date(end)
    logger.info(query, 'getHours')
    const arr = await this.Model.find(query).select(['uid', 'start', 'end', 'type', 'school', 'classId', 'substituteTeacher']).limit(1000).sort({start: 1})
    const uids = uid.$in ? uid.$in : [uid]
    return arr
      .filter((v: any) => {
        // substituteTeacher
        if (uids.includes(v.substituteTeacher)) return true
        // 需要忽略学校名下直播课，只取出报名过的课
        if (!Agl.workshopTypes.includes(v.type) && (v.school || v.classId)) return false
        return true
      })
      .map((v: any) => {
        if (uid.$in) return {uid: v.uid, hour: [v.start, v.end], type: v.type, substitute: uids.includes(v.substituteTeacher)} // 批量查询多个老师
        else return [v.start, v.end]
      })
  }
  async sendPromptMail(session: any, questions: any, params: Params) {
    let url = questions[0].data
    if (!url) return
    let index = url.lastIndexOf('/')
    let insertId = url.substring(index + 1, url.length)
    let insert_name = ''
    if (url.indexOf('session') > -1) {
      let data: any = await this.Model.findOne({_id: insertId})
      insert_name = data.name
    }
    if (url.indexOf('booking') > -1) {
      let data: any = await this.app.service('service-pack').Model.findOne({_id: insertId})
      insert_name = data.name
    }
    const inviteCode = params.user?.inviteCode
    if (inviteCode) {
      url = url + '?inviteCode=' + inviteCode
    }
    for (let i = 0; i < session.reg.length; i++) {
      const item = session.reg[i]
      const user = await this.app.service('users').uidToInfo(item._id)
      this.app.service('notice-tpl').send(
        'RecommendationOfPremium',
        {_id: user._id, email: user.email},
        {
          username: user.name.join(' '),
          current_name: session.name,
          insert_name: insert_name,
          url: url,
        }
      )
    }
  }
  // promopts 购买支付成功后插入
  // await App.service("session").insertPrompts({_id: 'session._id', promptId: 'prompts._id', index: '插入的位置'})
  async patchInsertPrompts({_id, promptId, index}: any, params: Params) {
    const doc: any = Acan.clone(await this.Model.findById(_id))
    const {pages, questions, materials}: any = await this.app.service('prompts').Model.findById(promptId)
    doc.task.pages.splice(index, 0, ...pages)
    doc.questions.push(...questions)
    const post: any = {'task.pages': doc.task.pages, questions: doc.questions}
    doc.materials.push(...materials)
    await this.app.service('prompts').Model.updateOne({_id: promptId}, {$inc: {used: 1}}) // 增加使用量
    return await this.patch(_id, post, params)
  }
  // 插入quickSession
  async patchNewPrompt({_id, index, page, questions, materialsList}: any, params: Params) {
    const inviteCode = params.user?.inviteCode
    const doc: any = Acan.clone(await this.Model.findById(_id))
    // 插入ppt, session.task.pages
    page._id = new ObjectId().toString()
    doc.task.pages.splice(index, 0, page)
    // 插入互动题, session.questions
    for (const o of questions) {
      o._id = new ObjectId().toString()
      o.page = page._id
      if (o.type === 'website') {
        const ubj = new URL(o.data)
        ubj.searchParams.set('inviteCode', inviteCode)
        ubj.searchParams.set('inviteSource', 'new_prompt')
        ubj.searchParams.set('inviteSourceId', _id)
        if (doc.classId && doc.school) {
          ubj.searchParams.set('schoolInviter', doc.school)
        }
        o.data = ubj.href
      }
    }
    doc.questions.push(...questions)
    this.sendPromptMail(doc, questions, params)
    const post: any = {'task.pages': doc.task.pages, questions: doc.questions}
    // 插入materials
    if (!Acan.isEmpty(materialsList)) {
      doc.materials.push({_id: new ObjectId().toString(), page: page._id, list: materialsList})
      post.materials = doc.materials
    }
    return await this.patch(_id, post, params)
  }

  async getListByIds({ids}: any, params: Params) {
    let list: Array<any> = await this.Model.find({_id: {$in: ids}})
      .select(this.selectList)
      .lean()
    for (const one of list) {
      await this.ext(one, params)
    }
    return list
  }

  // ended状态24小时后 学生未评价默认好评
  async mentorAutoRate() {
    this.Model.find({
      del: false,
      type: {
        $in: ['bookingStuTask', 'bookingStuPdTask'],
      },
      status: 'close',
      ended: {
        $lt: new Date(Date.now() - 24 * 60 * 60 * 1000),
        $gt: new Date(Date.now() - (24 * 60 + 3) * 60 * 1000),
      },
    })
      .populate('booking', 'booker', this.app.service('service-booking').Model)
      .exec(async (err, docs) => {
        for (let i = 0; i < docs.length; i++) {
          const item: any = docs[i]
          let count = await this.app.service('service-rating').Model.count({session: item._id, booker: item.booking.booker})
          if (count == 0) {
            await this.app.service('service-rating').Model.create({
              session: item._id,
              booking: item.booking._id,
              servicer: item.uid,
              booker: item.booking.booker,
              feel: true,
            })
          }
        }
      })
  }

  getSubstituteCancelBy({result}: any, params: Params) {
    if (result?.uid == params?.user?._id) {
      return 'owner'
    }
    if (result?.substituteTeacher == params?.user?._id) {
      return 'teacher'
    }
    if (result?.substituteAdmin == params?.user?._id) {
      return 'admin'
    }
  }

  // 代课 开课前3小时 管理员未审批 匹配未成功 取消代课申请
  substitute3HoursCancel(params: Params) {
    this.Model.find({
      substituteWithin: false,
      substituteTeacherStatus: 0,
      // $or: [{substituteReminder: false}, {substituteReminder: {$exists: false}}],
      start: {$lt: new Date(Date.now() + 3 * 60 * 60 * 1000)},
    }).then(async (rs: any) => {
      for (let i = 0; i < rs.length; i++) {
        let {substituteAdminStatus} = rs[i]
        if (substituteAdminStatus == 1) {
          // pending
          this.substituteSend(rs[i], 'SubstituteMatchingUnsuccessful(admin)', params)
          this.substituteSend(rs[i], 'SubstituteMatchingUnsuccessful(teacher)', params)
        }
        if (substituteAdminStatus == 0) {
          // request
          this.substituteSend(rs[i], 'RejectOfSubstituteTeachingRequest', params)
        }

        this.handleServicePackReturn({doc: rs[i]}, params)

        await this.Model.updateOne(
          {_id: rs[i]._id},
          {
            $unset: {
              substituteWithin: '',
              substituteTeacher: '',
              substituteTeacherStatus: '',
              substituteTeacherMessage: '',
              substituteAdmin: '',
              substituteAdminStatus: '',
              substituteAdminMessage: '',
              substitutePackUser: '',
              substituteServicePackSnapshot: '',
              substituteServicePackUserSnapshot: '',
              substituteSubject: '',
              substituteTopic: '',
              substitutePush: '',
              substituteDuration: '',
              substitutePushTime: '',
              substitutePushAll: '',
              substitutePriorityPush: '',
              substituteReminder: '',
              substituteNoCompensation: '',
              zoom: '',
            },
          }
        )
      }
    })
  }

  async substituteServiceUse({doc, packUser, substituteNoCompensation}: any, params: TxnParams) {
    const options = Acan.getTxnOptions(params)

    let {start, end, uid} = doc
    let {total, used, snapshot, country, city} = packUser
    let remaining = total - used
    let duration = (end - start) / 1000 / 60
    let compensation
    if (snapshot.isOnCampus) {
      // 线下
      if (!country || !city) {
        return {
          success: false,
          message: 'Service not available on campus',
        }
      }
      let campus: any = await this.app.service('campus-location').Model.findOne({country, city}, null, options)
      compensation = campus.compensationHour * 60
      if (substituteNoCompensation) {
        compensation = 0
      }
      duration = duration + compensation
    }
    if (duration > remaining) {
      return {
        success: false,
        message: 'Not enough service usage',
      }
    } else {
      await this.app
        .service('service-pack-user-data')
        .usedSubstitute({packUser: packUser._id, times: duration, type: 'booking', servicer: uid, compensation: compensation, oldSession: doc}, params)
      return {
        success: true,
        duration,
      }
    }
  }

  // 代课 管理员待审核数量
  async getSubstituteAdminCount({school}: any, params: Params) {
    let count = await this.Model.count({
      substituteAdmin: params.user?._id,
      substituteAdminStatus: 0,
      substituteWithin: false,
      school,
    })
    return count
  }
  // 取消代课 处理服务包退时间 2小时判断
  async handleServicePackReturn({doc}: any, params: TxnParams) {
    let {start, substitutePackUser, substituteDuration} = doc
    let startDate: any = new Date(start)
    let now: any = new Date()
    var hours = (startDate - now) / 1000 / 60 / 60
    if (hours > 2) {
      await this.app.service('service-pack-user-data').addSubstitute(
        {
          packUser: substitutePackUser,
          isNew: false,
          type: 'cancel',
          times: substituteDuration,
        },
        params
      )
    }
  }
  // 代课老师取消 开课不足2小时 取消代课 退还服务包时间
  async handleCancelByTeacher({doc}: any, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    let {start, substitutePackUser, substituteDuration} = doc
    let startDate: any = new Date(start)
    let now: any = new Date()
    var hours = (startDate - now) / 1000 / 60 / 60
    if (hours < 2) {
      await this.app.service('service-pack-user-data').addSubstitute(
        {
          packUser: substitutePackUser,
          isNew: false,
          type: 'cancel',
          times: substituteDuration,
        },
        params
      )
      await this.Model.updateOne(
        {
          _id: doc._id,
        },
        {
          $unset: {
            substituteWithin: '',
            substituteTeacher: '',
            substituteTeacherStatus: '',
            substituteTeacherMessage: '',
            substituteAdmin: '',
            substituteAdminStatus: '',
            substituteAdminMessage: '',
            substitutePackUser: '',
            substituteServicePackSnapshot: '',
            substituteServicePackUserSnapshot: '',
            substituteSubject: '',
            substituteTopic: '',
            substitutePush: '',
            substituteDuration: '',
            substitutePushTime: '',
            substitutePushAll: '',
            substitutePriorityPush: '',
            substituteReminder: '',
            zoom: '',
          },
        },
        options
      )
    }
  }

  // 校外代课 老师推送 每3分钟推送10人 7+3
  substitutePush(params?: Params) {
    this.Model.find({
      substituteWithin: false,
      substituteTeacher: {$exists: false},
      substituteAdminStatus: 1,
      $or: [{substitutePushTime: {$exists: false}}, {substitutePushTime: {$lt: new Date(Date.now() - 3 * 60 * 1000)}}],
      substitutePushAll: {$exists: false},
      // _id: '674e7c6c29a43d6993cf0d48',
    }).then(async (rs: any) => {
      for (let i = 0; i < rs.length; i++) {
        const item = rs[i]
        let teacherList = await this.app
          .service('service-conf')
          .getTeachersByPack({packUserId: item.substitutePackUser, subject: item.substituteSubject, topic: item.substituteTopic}, {user: {_id: item.uid}})
        // 过滤已推送的 及已排除的
        let substituteExclude = item.substituteExclude || []
        let substitutePush = item.substitutePush || []
        let substituteExcludeUids = substitutePush.concat(substituteExclude)
        teacherList.data = teacherList.data.filter((v: any) => !substituteExcludeUids.includes(v._id))
        // 排除时间已经重叠的老师
        const $in = teacherList.data.map((v: any) => v._id)
        const data: any = await this.app.service('service-booking').getCheckServicersHours({uid: {$in}, start: item.start, end: item.end})
        const uids = data.map((v: any) => v.uid)
        teacherList.data = teacherList.data.filter((v: any) => !uids.includes(v._id))

        let priorityPush = []
        if (item.substituteServicePackSnapshot.isOnCampus) {
          let lastSession = []
          if (!item.substitutePriorityPush) {
            // 线下 先处理优先匹配流程(先推送同地点时间范围内老师,省去路程补贴)
            lastSession = await this.Model.find({
              end: {$gte: new Date(new Date(item.start).getTime() - 1 * 60 * 60 * 1000), $lt: new Date(item.start)},
              substituteWithin: false,
              substituteTeacherStatus: 1,
              'substituteServicePackSnapshot.isOnCampus': true,
              'substituteServicePackUserSnapshot.place_id': item.substituteServicePackUserSnapshot.place_id,
            })
            let lastSessionUidList = lastSession.map((v: any) => v.substituteTeacher)
            priorityPush = teacherList.data.filter((v: any) => lastSessionUidList.includes(v._id))

            if (priorityPush.length > 0) {
              this.substituteTeacherSend({doc: item, users: priorityPush}, params)
              substituteExcludeUids = substituteExcludeUids.concat(priorityPush.map((v: any) => v._id))
              await this.Model.updateOne(
                {
                  _id: item._id,
                },
                {
                  $set: {
                    substitutePushTime: new Date(),
                    substitutePush: substituteExcludeUids.filter((e: any) => !substituteExclude.includes(e)),
                    substitutePriorityPush: true,
                  },
                }
              )
              continue
            }
          }

          // 线下过滤服务半径
          if (item.substitutePriorityPush || lastSession.length == 0) {
            let packUser: any = await this.app.service('service-pack-user').Model.findOne({_id: item.substitutePackUser})
            let uidList = teacherList.data.map((v: any) => new ObjectId(v._id))
            let teacherUidList = await this.app
              .service('campus-location')
              .getPushTeacherByRadius({country: packUser.country, city: packUser.city, place_id: packUser.place_id, uids: uidList}, params)
            teacherList.data = teacherList.data.filter((v: any) => teacherUidList.includes(v._id))
          }
        }

        let teacherRate = teacherList.data.filter((v: any) => v.count.rating > 0)
        let teacherNoRate = teacherList.data.filter((v: any) => v.count.rating == 0)
        let teacherRatePush: any = []
        let teacherNoRatePush: any = []
        if (teacherRate.length < 7) {
          teacherNoRatePush = teacherNoRate.slice(0, 10 - teacherRate.length)
        } else {
          teacherNoRatePush = teacherNoRate.slice(0, 3)
        }
        if (teacherNoRate.length < 3) {
          teacherRatePush = teacherRate.slice(0, 10 - teacherNoRate.length)
        } else {
          teacherRatePush = teacherRate.slice(0, 7)
        }

        let teacherPush = [...teacherRatePush, ...teacherNoRatePush]
        if (teacherPush.length > 0) {
          this.substituteTeacherSend({doc: item, users: teacherPush}, params)
          substituteExcludeUids = substituteExcludeUids.concat(teacherPush.map((v: any) => v._id))
          await this.Model.updateOne(
            {
              _id: item._id,
            },
            {
              $set: {
                substitutePushTime: new Date(),
                substitutePush: substituteExcludeUids.filter((e: any) => !substituteExclude.includes(e)),
                substitutePriorityPush: true,
              },
            }
          )
        } else {
          // push结束标记
          await this.Model.updateOne(
            {
              _id: item._id,
            },
            {
              $set: {
                substitutePushAll: true,
                substitutePushTime: new Date(),
                substitutePriorityPush: true,
              },
            }
          )
        }
      }
    })
  }
  async substituteSend(doc: any, tpl: string, params: TxnParams): Promise<any> {
    const options = Acan.getTxnOptions(params)
    let {_id: sessionId, type, name, school, uid, premium, substituteAdmin, substituteTeacher, start} = doc
    let user: any
    let url = ''
    let ownerSchool: any = await this.app.service('school-user').Model.findOne({school, uid}, null, options)
    let owner: any = await this.app.service('users').Model.findOne({_id: uid}, null, options)
    let adminSchool: any = await this.app.service('school-user').Model.findOne({school, uid: substituteAdmin}, null, options)
    let teacherSchool: any = await this.app.service('school-user').Model.findOne({school, uid: substituteTeacher}, null, options)
    let teacher: any = await this.app.service('users').Model.findOne({_id: substituteTeacher}, null, options)
    if (tpl === 'RejectOfSubstituteTeachingRequest') {
      url = this.getSessionUrl({_id: sessionId, type}, {})
      user = ownerSchool
    }
    if (tpl === 'CancellationOfSubstituteService') {
      url = `${SiteUrl}/v2/home/<USER>
      user = teacher
    }
    if (tpl === 'SubstituteMatchingUnsuccessful(admin)') {
      url = `${SiteUrl}/v2/substitute/track?tab=pending`
      user = adminSchool
    }
    if (tpl === 'SubstituteMatchingUnsuccessful(teacher)') {
      url = this.getSessionUrl({_id: sessionId, type}, {})
      user = ownerSchool
    }
    if (tpl === 'CancellationOfSubstituteService(97)') {
      url = `${SiteUrl}/v2/substitute/track`
      if (doc?.substituteTeacherStatus == 1) {
        url = `${SiteUrl}/v2/substitute/track?tab=matched`
      }
      if (doc?.substituteTeacherStatus == 0) {
        url = `${SiteUrl}/v2/substitute/track?tab=pending`
      }
      user = adminSchool
    }
    if (tpl === 'TerminationOfSubstituteServiceByServiceProvider(To admin)') {
      let startDate: any = new Date(start)
      let now: any = new Date()
      var hours = (startDate - now) / 1000 / 60 / 60
      if (hours > 2) {
        url = this.getSessionUrl({_id: sessionId, type}, {})
      } else {
        url = `${SiteUrl}/v2/substitute/track?tab=matched`
      }
      user = adminSchool
    }
    if (tpl === 'InvitedForSubstituteTeaching') {
      url = `${SiteUrl}/v2/substitute/slides/${sessionId}`
      user = teacherSchool
    }
    if (tpl === 'SubstituteInvitationRejected') {
      url = `${SiteUrl}/v2/detail/session/${sessionId}?back=/home/<USER>
      user = ownerSchool
    }
    if (tpl === 'RequestForSubstituteTeacher') {
      url = `${SiteUrl}/v2/detail/session/${sessionId}?back=/substitute/track&subform=admin`
      user = adminSchool
    }
    if (tpl === 'ApprovalOfSubstituteTeachingRequest') {
      url = this.getSessionUrl({_id: sessionId, type}, {})
      user = ownerSchool
    }

    return await queueForCommit(
      this.app,
      'notice-tpl',
      'mailto',
      [tpl, user.email, {username: user.name.join(' '), name, ownername: owner.name.join(' '), url}, params?.user?._id],
      params
    )
  }
  // 代课老师推送提醒
  async substituteTeacherSend({doc, users}: any, params?: Params): Promise<any> {
    let {_id: sessionId, name, school, uid, premium, substituteServicePackSnapshot, substitutePackUser, start, end, substituteMatched} = doc
    let {isOnCampus} = substituteServicePackSnapshot
    let tpl = 'ReminderToConfirmTheMatchedBooking'
    let url = `${SiteUrl}/v2/detail/session/${sessionId}?tab=substitute&subtab=scheduled&subform=subTeacher&subType=order`
    let location = ''
    if (isOnCampus) {
      let packUser: any = await this.app.service('service-pack-user').Model.findOne({_id: substitutePackUser})
      location = `(at the location of ${packUser.address})`
    }

    for (let i = 0; i < users.length; i++) {
      const {_id} = users[i]
      let user: any = await this.app.service('users').Model.findOne({_id})
      let username = user.name.join(' ')
      let option: any = {timeZone: user.timeZone || 'Asia/Shanghai', timeZoneName: 'short', hour12: false}
      let startTime = new Date(start).toLocaleString('en', option)
      let endTime = new Date(end).toLocaleString('en', option)

      await this.app.service('notice-tpl').mailto(tpl, user, {username, name, startTime, endTime, location, url}, params?.user?._id)
    }
  }

  // 精品lecture课程结束后  相关通知发送
  async sendPremiumNotice(result: any, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const {booking, servicePack, reg, name: lecture_name, sid} = result
    if (booking) {
      let bookingData: any = await this.app.service('service-booking').Model.findOne({_id: booking}, null, options)
      let packUser: any = await this.app.service('service-pack-user').Model.findOne({_id: bookingData.packUser}, null, options)
      if (!packUser.pid) {
        return
      }
      let packUserParent: any = await this.app.service('service-pack-user').Model.findOne({_id: packUser.pid}, null, options)
      let {snapshot, uid} = packUserParent
      let servicePackId = false
      for (let i = 0; i < snapshot.contentOrientated.length; i++) {
        if (snapshot.contentOrientated[i].premium == packUser.premium && snapshot.contentOrientated[i].servicePack) {
          servicePackId = snapshot.contentOrientated[i].servicePack
          break
        }
      }
      if (servicePackId) {
        const user = await this.app.service('users').uidToInfo(uid)
        let packUser1v1: any = await this.app.service('service-pack-user').Model.findOne({uid, 'snapshot._id': servicePackId}, null, options)
        let url = `${SiteUrl}/v2/service/pack/${servicePackId}`
        if (packUser1v1) {
          url = `${SiteUrl}/v2/s/${sid}`
        }
        await queueForCommit(
          this.app,
          'notice-tpl',
          'mailto',
          ['ReminderToSchedule1v1MentorSessionBundledWithAnEndedPremiumLecture', user.email, {username: user.name.join(' '), lecture_name, url}],
          params
        )
      }
    }
    if (servicePack) {
      for (let i = 0; i < reg.length; i++) {
        const regData = reg[i]
        let user = await this.app.service('users').uidToInfo(regData._id)
        let url = `${SiteUrl}/v2/s/${sid}`

        await queueForCommit(
          this.app,
          'notice-tpl',
          'mailto',
          ['ReminderToSchedule1v1MentorSessionBundledWithAnEndedPremiumLecture', user.email, {username: user.name.join(' '), lecture_name, url}],
          params
        )
      }
    }
  }

  // 在课堂中开启zoom会议
  async patchCreateZoom({_id, sid, start, name, zoom}: any, params: Params) {
    const rs = await this.app.service('zoom-meet').create({sid, start, name, ...zoom}, params)
    return await this.patch(_id, {zoom: rs}, {inside: true})
  }

  // #5496 成功排课后 zoom授权过期发送邮件
  async sendZoomNotice(result: any, params: TxnParams) {
    const {zoom, uid, name, _id, type} = result
    if (zoom && !zoom.id) {
      let user = await this.app.service('users').uidToInfo(uid)
      let url = this.getSessionUrl({_id, type}, params)
      await queueForCommit(
        this.app,
        'notice-tpl',
        'mailto',
        ['ReminderOfExpirationOfZoomAccountAuthorization', user, {username: user.name.join(' '), name, url}],
        params
      )
      // await this.app.service('notice-tpl').mailto('ReminderOfExpirationOfZoomAccountAuthorization', user, {username: user.name.join(' '), name, url})
    }
  }
  async sendCancelNotice({doc}: any, params?: TxnParams) {
    console.log('🚀 ~ sendCancelNotice ~ doc:', doc)
    let {uid, name, substituteTeacherStatus, substituteTeacher} = doc
    const owner = await this.app.service('users').uidToInfo(uid)
    let startTime = new Date(doc.start)
    let endTime = new Date(doc.end)
    let option: any = {timeZone: owner.timeZone || 'Asia/Shanghai', timeZoneName: 'short', hour12: false}
    let time = `${startTime.toLocaleString('en', option)} - ${endTime.toLocaleString('en', option)}`
    console.log('🚀 ~ sendCancelNotice ~ time:', time)
    queueForCommit(this.app, 'notice-tpl', 'send', ['MentoringServiceCanceled', {_id: owner._id, email: owner.email}, {name, time}], params)
    if (substituteTeacherStatus == 1 && substituteTeacher) {
      let teacher = await this.app.service('users').uidToInfo(substituteTeacher)
      let option: any = {timeZone: teacher.timeZone || 'Asia/Shanghai', timeZoneName: 'short', hour12: false}
      let time = `${startTime.toLocaleString('en', option)} - ${endTime.toLocaleString('en', option)}`
      queueForCommit(this.app, 'notice-tpl', 'send', ['MentoringServiceCanceled', {_id: teacher._id, email: teacher.email}, {name, time}], params)
    }
  }

  // 判断代课老师是否有路费补贴
  async getIsCompensation({session, substituteTeacher}: any, params?: TxnParams): Promise<any> {
    const options = Acan.getTxnOptions(params)

    let sessionData: any = await this.Model.findOne({_id: session}, null, options)
    let lastSession = await this.Model.find(
      {
        end: {$gte: new Date(new Date(sessionData.start).getTime() - 1 * 60 * 60 * 1000), $lt: new Date(sessionData.start)},
        substituteWithin: false,
        substituteTeacherStatus: 1,
        'substituteServicePackSnapshot.isOnCampus': true,
        'substituteServicePackUserSnapshot.place_id': sessionData.substituteServicePackUserSnapshot.place_id,
      },
      null,
      options
    )
    let lastSessionUidList = lastSession.map((v: any) => v.substituteTeacher)
    return lastSessionUidList.includes(substituteTeacher)
  }
}
