import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {BadRequest, NotFound} from '@feathersjs/errors'
import {TxnParams} from '../../hooks/dbTransactions'

interface CreateGiftCardLog {
  uid: string
  tab: string
  source: string
  category: string
  value: number
  businessId?: string
  isSchool: boolean
  snapshot?: any
  reserveBalance?: boolean
}
export class GiftCardLog extends Service {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async createGiftCardLog(data: CreateGiftCardLog, params?: TxnParams): Promise<any> {
    const {uid, tab, source, category, value, businessId, snapshot, isSchool, reserveBalance} = data

    if (!uid || !tab || !source || !category || value === undefined) {
      throw new BadRequest('Missing required fields')
    }

    const numericValue = Number(value)

    const modelName = isSchool ? 'school-plan' : 'users'
    const query =
      numericValue < 0
        ? {
            _id: uid,
            $expr: {$gte: [{$add: [{$ifNull: ['$giftCardBalance', 0]}, numericValue]}, 0]},
          }
        : {_id: uid}

    const options = Acan.getTxnOptions(params)
    const updateOptions: any = {
      new: true,
      upsert: false,
      ...options,
    }

    const updateData: any = {$inc: {giftCardBalance: numericValue}}
    if (reserveBalance) {
      updateData.$inc.reserveGiftCardBalance = numericValue * -1
    }
    // Update user/school balance
    const updatedAccount = (await this.app.service(modelName).Model.findOneAndUpdate(query, updateData, updateOptions)) as any

    if (!updatedAccount) {
      const account = isSchool
        ? await this.app.service('school-plan').Model.findOne({_id: uid}, null, options)
        : await this.app.service('users').Model.findOne({_id: uid}, null, options)

      if (!account) {
        throw new NotFound('User or school not found')
      } else {
        throw new BadRequest('Insufficient gift card balance')
      }
    }

    await this.Model.create(
      [
        {
          uid,
          tab,
          source,
          category,
          value: numericValue,
          total: updatedAccount.giftCardBalance,
          businessId,
          snapshot,
          isSchool: isSchool || false,
        },
      ],
      options
    )
  }

  async releaseReservedBalance(amount: number, buyer: string, isSchool: boolean, params?: TxnParams): Promise<any> {
    const numericAmount = Number(amount)

    if (!amount || !buyer) {
      throw new BadRequest('Amount and UID are required')
    }

    const options = Acan.getTxnOptions(params)
    const updateOptions: any = {
      new: true,
      upsert: false,
      ...options,
    }

    // Update user/school reserve balance
    const modelName = isSchool ? 'school-plan' : 'users'
    const query = {_id: buyer}
    const updateData: any = {$inc: {reserveGiftCardBalance: numericAmount * -1}}

    const updatedAccount = (await this.app.service(modelName).Model.findOneAndUpdate(query, updateData, updateOptions)) as any

    if (!updatedAccount) {
      throw new NotFound('User or gift card not found')
    }
    return updatedAccount
  }
}
