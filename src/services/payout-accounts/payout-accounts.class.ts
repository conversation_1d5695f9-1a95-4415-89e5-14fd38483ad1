import {Id, NullableId, Paginated, Params, ServiceMethods} from '@feathersjs/feathers'
import {Application} from '../../declarations'
const {GeneralError, BadRequest, Forbidden, NotFound} = require('@feathersjs/errors')
import hook from '../../hook'

interface PayoutAccountData {
  _id?: string
  userId?: string // For individual users
  schoolId?: string // For schools
  airwallexBeneficiaryId?: string
  minimumPayoutAmount?: number // in cents, default 500 ($5)
  lastPayoutDate?: Date
  totalPayoutAmount?: number // lifetime total in cents
  payoutCount?: number // number of successful payouts
  isActive?: boolean
  createdAt?: Date
  updatedAt?: Date
}

interface ServiceOptions {}

export class PayoutAccounts implements ServiceMethods<PayoutAccountData> {
  app: Application
  options: ServiceOptions
  Model: any

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options
    this.app = app
    this.Model = app.get('mongooseClient').model('PayoutAccount')
  }

  async find(params: Params): Promise<PayoutAccountData[] | Paginated<PayoutAccountData>> {
    const user = params.user

    if (!user) {
      throw new Forbidden('Not authorized to access payout accounts')
    }

    const {query = {}} = params

    if (query.$sys && !this.isAdmin(params)) {
      throw new Forbidden('Not authorized to access payout accounts')
    }

    if (query.$sys) {
      delete query.$sys
    } else if (query.schoolId) {
      this.verifySchoolPermission(query.schoolId, params.user)
    } else {
      query.userId = user._id
    }

    try {
      const results = await this.Model.find(query).sort({createdAt: -1})

      return results
    } catch (error: any) {
      throw new GeneralError(`Error retrieving payout accounts: ${error.message}`)
    }
  }

  async get(id: Id, params: Params): Promise<PayoutAccountData> {
    try {
      const account = await this.Model.findById(id)

      if (!account) {
        throw new NotFound('Payout account not found')
      }

      // Check permissions
      await this.verifyAccountPermission(account, params.user)

      return account
    } catch (error: any) {
      if (error.name === 'NotFound' || error.name === 'Forbidden') {
        throw error
      }
      throw new GeneralError(`Error retrieving payout account: ${error.message}`)
    }
  }

  async create(data: PayoutAccountData): Promise<PayoutAccountData> {
    if (!data.airwallexBeneficiaryId) {
      throw new BadRequest('Stripe account ID is required')
    }

    // Set defaults
    const accountData = {
      ...data,
      minimumPayoutAmount: data.minimumPayoutAmount || 500, // $5 default
      totalPayoutAmount: 0,
      payoutCount: 0,
      isActive: true,
    }

    try {
      const account = await this.Model.create(accountData)
      return account
    } catch (error: any) {
      throw new GeneralError(`Error creating payout account: ${error.message}`)
    }
  }

  async update(id: NullableId, data: PayoutAccountData, params: Params): Promise<PayoutAccountData> {
    return this.patch(id, data, params)
  }

  async patch(id: NullableId, data: PayoutAccountData, params: Params): Promise<PayoutAccountData> {
    if (!id) {
      throw new BadRequest('Account ID is required')
    }

    try {
      const existingAccount = await this.Model.findById(id)
      if (!existingAccount) {
        throw new NotFound('Payout account not found')
      }

      // Prepare update data
      const updateData: any = {
        ...data,
        updatedAt: new Date(),
      }

      // Remove fields that shouldn't be updated directly
      delete updateData._id
      delete updateData.createdAt
      delete updateData.userId
      delete updateData.schoolId

      const updatedAccount = await this.Model.findByIdAndUpdate(id, {$set: updateData}, {new: true, runValidators: true})

      return await this.get(updatedAccount._id, params)
    } catch (error: any) {
      if (error.name === 'NotFound' || error.name === 'Forbidden') {
        throw error
      }
      throw new GeneralError(`Error updating payout account: ${error.message}`)
    }
  }

  async remove(id: NullableId, params: Params): Promise<PayoutAccountData> {
    if (!id) {
      throw new BadRequest('Account ID is required')
    }

    try {
      const account = await this.Model.findById(id)
      if (!account) {
        throw new NotFound('Payout account not found')
      }

      // Check permissions
      await this.verifyAccountPermission(account, params.user)

      // Soft delete - mark as inactive instead of removing
      const updatedAccount = await this.Model.findByIdAndUpdate(
        id,
        {
          $set: {
            isActive: false,
            updatedAt: new Date(),
          },
        },
        {new: true}
      )

      return updatedAccount
    } catch (error: any) {
      if (error.name === 'NotFound' || error.name === 'Forbidden') {
        throw error
      }
      throw new GeneralError(`Error deactivating payout account: ${error.message}`)
    }
  }

  // Custom methods for payout account management

  async findByUser(userId: string): Promise<PayoutAccountData | null> {
    try {
      return await this.Model.findOne({userId, isActive: true})
    } catch (error: any) {
      throw new GeneralError(`Error finding payout account by user: ${error.message}`)
    }
  }

  async getEligibleForPayout(): Promise<PayoutAccountData[]> {
    try {
      // Get accounts that are active, onboarding completed, and payouts enabled
      const accounts = await this.Model.find({
        isActive: true,
      })

      // Filter accounts that meet minimum payout threshold
      const eligibleAccounts = []
      for (const account of accounts) {
        const currentBalance = await this.getCurrentBalance(account)
        if (currentBalance >= account.minimumPayoutAmount) {
          eligibleAccounts.push(account)
        }
      }

      return eligibleAccounts
    } catch (error: any) {
      throw new GeneralError(`Error getting eligible payout accounts: ${error.message}`)
    }
  }

  async updatePayoutStats(accountId: string, payoutAmount: number): Promise<void> {
    try {
      await this.Model.findByIdAndUpdate(accountId, {
        $inc: {
          totalPayoutAmount: payoutAmount,
          payoutCount: 1,
        },
        $set: {
          lastPayoutDate: new Date(),
          updatedAt: new Date(),
        },
      })
    } catch (error: any) {
      throw new GeneralError(`Error updating payout stats: ${error.message}`)
    }
  }

  // Business logic methods moved from model

  async getCurrentBalance(account: PayoutAccountData): Promise<number> {
    try {
      if (account.userId) {
        const user = await this.app.service('users').get(account.userId)
        return user?.balance || 0
      } else if (account.schoolId) {
        const school = await this.app.service('school-plan').get(account.schoolId)
        return (school?.balance || 0) + (school?.commission || 0) + (school?.income || 0)
      }
      return 0
    } catch (error: any) {
      console.error(`Error getting current balance: ${error.message}`)
      return 0
    }
  }

  async isEligibleForPayout(account: PayoutAccountData): Promise<boolean> {
    if (!(account.isActive === true)) {
      return false
    }

    const currentBalance = await this.getCurrentBalance(account)
    return currentBalance >= (account.minimumPayoutAmount || 500)
  }

  // Helper methods

  private async verifyAccountPermission(account: PayoutAccountData, user: any): Promise<void> {
    if (this.isAdmin({user})) {
      return // Admins can access all accounts
    }

    if (!user) {
      throw new Forbidden('Authentication required')
    }

    // Check if user owns the account or has school access
    if (account.userId && account.userId.toString() === user._id) {
      return
    }

    if (account.schoolId) {
      await this.verifySchoolPermission(account.schoolId.toString(), user)
      return
    }

    throw new Forbidden('Not authorized to access this payout account')
  }

  private async verifySchoolPermission(schoolId: string, user: any): Promise<void> {
    if (this.isAdmin({user})) {
      return
    }

    if (!user) {
      throw new Forbidden('Authentication required')
    }

    // Check if user is associated with this school and is an admin
    const schoolUser = await this.app.service('school-user').Model.findOne({
      school: schoolId,
      uid: user._id,
      status: 2,
      role: 'admin',
    })

    if (!schoolUser) {
      throw new Forbidden('Not authorized to perform operations for this school')
    }
  }

  private isAdmin(params: Params): boolean {
    return hook.roleHas(['sys', 'admin'])({params}) || hook.managerRoleHas(['admin', 'accountant'])({params})
  }
}
