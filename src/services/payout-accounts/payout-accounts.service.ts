// Initializes the `payout-accounts` service on path `/payout-accounts`
import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {PayoutAccounts} from './payout-accounts.class'
import createModel from '../../models/payout-accounts.model'
import hooks from './payout-accounts.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'payout-accounts': PayoutAccounts & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$regex', '$options', '$search'],
  }

  // Initialize our service with any options it requires
  app.use('/payout-accounts', new PayoutAccounts(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('payout-accounts')
  service.hooks(hooks)
}
