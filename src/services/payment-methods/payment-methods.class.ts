import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params, Id, NullableId} from '@feathersjs/feathers'
const {BadRequest, Forbidden, NotFound} = require('@feathersjs/errors')

export class PaymentMethods extends Service {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  // Get all payment methods for a user or school
  async find(params: Params): Promise<any> {
    const {schoolId} = params.query || {}
    const user = params.user || {}

    if (schoolId) {
      await this.app.service('stripe').verifySchoolPermission(schoolId, user)
    }

    // If schoolId is provided, get school payment methods
    // Otherwise, get user payment methods

    return super.find({
      ...params,
      query: {
        ...params.query,
        stripePaymentMethodId: {$ne: 'customer_record'},
        ...(schoolId ? {schoolId} : {userId: user._id}),
      },
    })
  }

  async get(id: Id, params: Params): Promise<any> {
    const paymentMethod = await super.get(id, params)

    const user = params.user || {}

    if (paymentMethod.schoolId) {
      await this.app.service('stripe').verifySchoolPermission(paymentMethod.schoolId, user)
    } else if (paymentMethod.userId.toString() !== user._id.toString()) {
      throw new Forbidden('Not authorized to access this payment method')
    }

    return paymentMethod
  }

  async create(data: any, params: Params): Promise<any> {
    const {stripePaymentMethodId, isDefault, schoolId} = data
    const user = params.user || {}

    if (!stripePaymentMethodId) {
      throw new BadRequest('Payment method ID is required')
    }

    const userId = schoolId ? null : user._id

    if (schoolId) {
      await this.app.service('stripe').verifySchoolPermission(schoolId, user)
    }

    const customerId = await this.app.service('stripe').getCustomerId(userId, schoolId)

    if (!customerId) {
      throw new NotFound('Customer not found')
    }

    const paymentMethod = await this.app.service('stripe').getPaymentMethod(stripePaymentMethodId, customerId)
    // Card details are extracted from stripe, and not passed directly from client/frontend
    const card = paymentMethod.card

    return super.create(
      {
        userId,
        schoolId: schoolId || null,
        stripeCustomerId: customerId,
        stripePaymentMethodId,
        cardBrand: card.brand,
        last4: card.last4,
        expMonth: card.exp_month,
        expYear: card.exp_year,
        fingerprint: card.fingerprint,
        isDefault: isDefault,
      },
      params
    )
  }

  async patch(id: NullableId, data: any, params: Params): Promise<any> {
    if (!id) return

    const allowedFields = ['isDefault']
    const filteredData = Object.keys(data)
      .filter((key) => allowedFields.includes(key))
      .reduce((obj, key) => {
        obj[key] = data[key]
        return obj
      }, {} as any)

    if (Object.keys(filteredData).length === 0) {
      throw new BadRequest('Only Default status field can be updated')
    }

    const paymentMethod = await this.get(id, params)

    if (filteredData.isDefault === true) {
      await this.app.service('stripe').setDefaultPaymentMethod({
        paymentMethodId: paymentMethod.stripePaymentMethodId,
        customerId: paymentMethod.stripeCustomerId,
        schoolId: paymentMethod.schoolId,
      })

      // Update all other payment methods for this customer to not be default
      if (paymentMethod.schoolId) {
        await this.Model.updateMany({schoolId: paymentMethod.schoolId, _id: {$ne: paymentMethod._id}}, {$set: {isDefault: false}})
      } else if (paymentMethod.userId) {
        await this.Model.updateMany({userId: paymentMethod.userId, _id: {$ne: paymentMethod._id}}, {$set: {isDefault: false}})
      }
    }

    return super.patch(id, filteredData, params)
  }

  async remove(id: NullableId, params: Params): Promise<any> {
    if (!id) return

    const paymentMethod = await this.get(id, params)

    await this.app.service('stripe').detachPaymentMethod({
      paymentMethodId: paymentMethod.stripePaymentMethodId,
      schoolId: paymentMethod.schoolId,
    })

    return super.remove(id, params)
  }
}
