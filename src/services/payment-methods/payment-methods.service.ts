// Initializes the `payment-methods` service on path `/payment-methods`
import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {PaymentMethods} from './payment-methods.class'
import createModel from '../../models/payment-methods.model'
import hooks from './payment-methods.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'payment-methods': PaymentMethods & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    multi: ['patch', 'remove'],
  }

  // Initialize our service with any options it requires
  app.use('/payment-methods', new PaymentMethods(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('payment-methods')

  service.hooks(hooks)
}
