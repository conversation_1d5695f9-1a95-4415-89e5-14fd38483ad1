// Initializes the `payout-accounts` service on path `/payout-accounts`
import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {PayoutTransactions} from './payout-transactions.class'
import createModel from '../../models/payout-transactions.model'
import hooks from './payout-transactions.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'payout-transactions': PayoutTransactions & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$regex', '$options', '$search'],
  }

  // Initialize our service with any options it requires
  app.use('/payout-transactions', new PayoutTransactions(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('payout-transactions')
  service.hooks(hooks)
}
