import * as authentication from '@feathersjs/authentication'
import {GeneralError} from '@feathersjs/errors'
import {HookContext} from '@feathersjs/feathers'
import hook from '../../hook'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks

export default {
  before: {
    all: [authenticate('jwt')],
    find: [
      (d: HookContext) => {
        const query = d.params.query ?? {}
        if (query.$sys) {
          if (!hook.roleHas(['sys', 'admin'])(d)) return Promise.reject(new GeneralError('Not allowed'))
          delete query.$sys
        } else {
          return Promise.reject(new GeneralError('Not allowed'))
        }
      },
    ],
    get: [
      (d: HookContext) => {
        const query = d.params.query ?? {}
        if (query.$sys) {
          if (!hook.roleHas(['sys', 'admin'])(d)) return Promise.reject(new GeneralError('Not allowed'))
          delete query.$sys
        } else {
          return Promise.reject(new GeneralError('Not allowed'))
        }
      },
    ],
    create: [
      async (d: HookContext) => {
        if (d.params.provider) {
          return Promise.reject(new GeneralError('Not allowed'))
        }
      },
    ],
    update: [
      async (d: HookContext) => {
        if (d.params.provider) {
          return Promise.reject(new GeneralError('Not allowed'))
        }
      },
    ],
    patch: [
      (d: HookContext) => {
        const query = d.params.query ?? {}
        if (query.$sys) {
          if (!hook.roleHas(['sys', 'admin'])(d)) return Promise.reject(new GeneralError('Not allowed'))
          delete query.$sys
        } else {
          return Promise.reject(new GeneralError('Not allowed'))
        }
      },
      async (context: HookContext) => {
        const {data, id} = context

        // Only allow editing of status field
        const allowedFields = ['status', 'adminNotes']
        const providedFields = Object.keys(data)
        const invalidFields = providedFields.filter((field) => !allowedFields.includes(field))

        if (invalidFields.length > 0) {
          throw new GeneralError(`Only ${allowedFields.join(', ')} fields can be modified. Invalid fields: ${invalidFields.join(', ')}`)
        }

        // If status is being changed, validate the transition
        if (data.status) {
          const currentRecord = await context.app.service('payout-transactions').get(id)
          const currentStatus = currentRecord.status
          const newStatus = data.status

          // Only allow pending <-> cancelled transitions
          const validTransitions = [
            {from: 'pending', to: 'cancelled'},
            {from: 'cancelled', to: 'pending'},
          ]

          const isValidTransition = validTransitions.some((transition) => transition.from === currentStatus && transition.to === newStatus)

          if (!isValidTransition) {
            throw new GeneralError(`Invalid status transition from '${currentStatus}' to '${newStatus}'. Only pending ↔ cancelled transitions are allowed.`)
          }

          if (newStatus === 'cancelled') {
            data.cancelledBy = context.params.user?._id
            if (!data.adminNotes) {
              data.adminNotes = 'Cancelled by admin'
            }
          } else if (newStatus === 'pending') {
            data.cancelledBy = null
            if (!data.adminNotes) {
              data.adminNotes = 'Restored to pending by admin'
            }
          }
        }

        return context
      },
    ],
    remove: [
      async (d: HookContext) => {
        if (d.params.provider) {
          return Promise.reject(new GeneralError('Not allowed'))
        }
      },
    ],
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
