import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
// Don't remove this comment. It's needed to format import lines nicely.

import hook from '../../hook'
const {authenticate} = authentication.hooks

export default {
  before: {
    all: [authenticate('jwt')],
    find: [
      (d: HookContext) => {
        const query = d.params.query ?? {}
        const user = d.params.user ?? {}
        // ACL
      },
    ],
    get: [hook.toClass],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
