// Initializes the `income-log` service on path `/income-log`
import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {IncomeLog} from './income-log.class'
import createModel from '../../models/income-log.model'
import hooks from './income-log.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'income-log': IncomeLog & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
  }

  // Initialize our service with any options it requires
  app.use('/income-log', new IncomeLog(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('income-log')

  service.hooks(hooks)
}
