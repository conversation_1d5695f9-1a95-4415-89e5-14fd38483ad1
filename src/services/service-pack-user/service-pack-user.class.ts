import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {NotFound} from '@feathersjs/errors'
import {TxnParams} from '../../hooks/dbTransactions'

export class ServicePackUser extends Service {
  app: Application
  selectList: String[]
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.selectList = ['-snapshot.linkSnapshot', '-snapshot.feedback', '-snapshot.approval', '-snapshot.attachments']
    this.app = app
  }
  toInfo(_id: String, dense: Boolean) {
    return this.Model.findById(_id).select(this.selectList.join(' '))
  }
  // 通过服务包ID查已购买的服务包
  async getByPack({pack}: any, params: Params) {
    const doc = Acan.clone(await this.Model.findOne({uid: params.user?._id, 'snapshot._id': pack}))
    if (!doc) return Promise.reject(new NotFound())
    await this.extUser(doc)
    return doc
  }
  // need remove
  getBuyByOrder(query: any, params: Params) {
    return this.buyByOrder(query, params)
  }
  // Lecture包按次数删除tasks
  async taskRemove({packUser, times}: any) {
    const {tasks}: any = await this.Model.findById(packUser).select(['tasks'])
    if (Acan.isEmpty(tasks)) return
    return tasks.splice(0, times)
  }
  // Lecture包补买插入顺序
  taskInsert(packUserDoc: any, taskIds: any) {
    const list = []
    for (const id of packUserDoc.tasks) {
      list.push({id, index: packUserDoc.taskIndex.indexOf(id)})
    }
    for (const id of taskIds) {
      list.push({id, index: packUserDoc.taskIndex.indexOf(id)})
    }
    Acan.arrSort(list, 'index')
    return list.map((v) => v.id)
  }
  // 认证精品课的上课列表，顺序处理逻辑 List of classes for certified premium courses, sequential processing logic
  taskSort(authDoc: any) {
    const {linkGroup, link} = authDoc.unitSnapshot
    const list: any = []
    // 组排序 Group sorting
    for (const group of linkGroup) {
      // 组内排序 Sort within group
      for (const o of link) {
        if (o.group !== group._id) continue
        if (!list.includes(o.id)) list.push(o.id) // 去重 Remove duplicates
      }
    }
    return list
  }
  // 主题服务包购买处理
  async contentOrientatedCreate({pid, order, contentOrientated, isPoint, servicePremium}: any, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const uid = params.user?._id
    const rs = []
    for (const one of contentOrientated) {
      // let pack: any
      // if (one.servicePack) pack = await this.app.service('service-pack').Model.findById(one.servicePack)
      // 获取认证的精品课快照数据 Get snapshot data of certified premium courses
      const authRs: any = await this.app.service('service-auth').Model.findById(one.premium, null, options)
      const post: any = {
        uid,
        order,
        pid,
        // price: one.price,
        isPoint,
        premium: one.premium,
        servicePremium,
        // total: one.times,
      }
      if (authRs) {
        post.snapshot = Acan.clone(authRs)
        //  Lecture包 需要计算出所有课件的排序 The Lecture package needs to calculate the order of all the courseware
        post.taskIndex = this.taskSort(post.snapshot)
        post.tasks = post.taskIndex
      }
      rs.push(Acan.clone(await this.create(post, Acan.mergeTxnParams(params))))
    }
    return rs
  }
  // 代课服务包创建子服务包
  async substituteCreate({packId, pid, order, country, city, isPoint}: any, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const uid = params.user?._id
    let packUserSubstitute: any = await this.app.service('service-pack-user').Model.findOne({uid, pid, country, city}, null, options)
    if (packUserSubstitute) {
      return {
        packUserSubstitute,
        isNew: false,
      }
    } else {
      const pack: any = await this.app.service('service-pack').Model.findById(packId, null, options)
      const post: any = {
        uid,
        order,
        pid,
        isPoint,
        snapshot: pack,
        country,
        city,
      }
      let packUserSubstitute: any = await this.create(post, Acan.mergeTxnParams(params))
      return {
        packUserSubstitute,
        isNew: true,
      }
    }
  }

  // 支付后成功创建用户购买的服务包，内部调用
  async buyByOrder({packId, order, session, price, point = 0, isPoint = false, mentorPack, sectionCount}: any, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const uid = params.user?._id
    const pack: any = await this.app.service('service-pack').Model.findById(packId, null, options)
    const sessionDoc: any = await this.app.service('session').Model.findById(session, null, options).select(['name', 'start', 'end'])
    const post: any = {
      uid,
      order,
      snapshot: Acan.clone(pack),
      price,
      point,
      isPoint,
    }
    if (sessionDoc) post.session = Acan.clone(sessionDoc)
    if (pack.type === 'serviceTask') {
      post.associatedTaskStatus = 'unassigned'
    }
    Acan.objClean(post)
    if (pack.associatedTask) {
      post.associatedTask = Acan.clone(pack.associatedTask)
    }
    // 更新服务包销量    Update service package sales
    await this.app.service('service-pack').incCount(packId, {'count.sold': 1, 'count.valid': 1}, params)
    const rs: any = Acan.clone(await this.create(post, params))
    if (pack.type === 'serviceTask') {
      const sections: any[] = []
      for (let index = 0; index < pack.sections.length; index++) {
        const isLocked = index > 0 && sectionCount === 1
        const element = pack.sections[index]
        const curSection: any = {
          sectionNo: index + 1,
          name: element.name,
          prompt: element.prompt,
          salesPrice: element.salesPrice,
          costPrice: element.costPrice,
          serviceTaskId: rs._id,
          uid,
          availableCredits: isLocked ? 0 : element.salesPrice,
          creditSources: isLocked
            ? undefined
            : [
                {
                  orderId: order,
                  total: element.salesPrice,
                  available: element.salesPrice,
                },
              ],
          taskDetails: {
            id: pack._id.toString(),
            name: pack.name,
            cover: pack.cover,
            mentoringType: pack.mentoringType,
          },
        }
        sections.push(curSection)
      }
      await this.app.service('section').Model.create(sections, options)
      if (mentorPack) {
        const mentorPackDoc: any = await this.app
          .service('service-pack-user')
          .Model.findOne({uid, 'snapshot._id': mentorPack}, null, options)
          .select(['_id'])
          .lean()
        if (!mentorPackDoc) throw new Error('Mentor pack not found')
        await this.Model.updateOne({_id: rs._id}, {$set: {pid: mentorPackDoc._id}}, options)
      }
    }
    if (pack.contentOrientatedEnable && !Acan.isEmpty(pack.contentOrientated)) {
      // 批量创建主题服务包下的Lecture包
      rs.childs = await this.contentOrientatedCreate(
        {
          order,
          pid: rs._id,
          contentOrientated: pack.contentOrientated,
          isPoint,
        },
        params
      )
    }
    return rs
  }

  async buyServiceTaskCredit({sectionId, amount, order}: {sectionId: string; amount: number; order: string}, params?: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const packUser: any = await this.Model.findOne({'sectionCreditDeficit.sectionId': sectionId}, null, options).select(['_id', 'sectionCreditDeficit']).lean()
    if (!packUser) {
      return Promise.reject(new Error('Section not found'))
    }
    if (packUser.sectionCreditDeficit.points * 100 !== amount) {
      return Promise.reject(new Error('Invalid credit amount'))
    }

    await Promise.all([
      this.Model.updateOne({_id: packUser._id}, {$unset: {sectionCreditDeficit: ''}}, options),
      this.app.service('section').buyDeficitCredit({sectionId, amount: amount / 100, order}, params),
    ])
  }

  // 判断服务包次数是否足够
  async checkTimes({_id, times}: any, params: Params) {
    const one: any = await this.Model.findById(_id).select(['total', 'used'])
    if (one.used + times > one.total) return Promise.reject(new Error('Not enough times'))
    return true
  }
  async extUser(one: any) {
    one.owner = await this.app.service('users').uidToInfo(one.uid)
    if (!one.owner) {
      await this.app.service('school-plan').Model.findOne({_id: one.uid})
    }
    if (one.snapshot.uid) {
      one.snapshot.owner = await this.app.service('users').uidToInfo(one.snapshot.uid)
    }
  }
  async getCheckFree({ids}: any, params: Params) {
    let res: any = {}
    for (let i = 0; i < ids.length; i++) {
      const _id = ids[i]
      let data: any = await this.app.service('service-pack-user-data').Model.find({packUser: _id, payMethod: {$in: ['cash', 'point']}})
      let childData: any = []
      let childIds: any = await this.Model.find({pid: _id}).select(['_id'])
      if (childIds.length) {
        childIds = childIds.map((v: any) => v._id)
        childData = await this.app.service('service-pack-user-data').Model.find({packUser: {$in: childIds}, payMethod: {$in: ['cash', 'point']}})
      }
      if (data.length > 0 || childData.length > 0) {
        res[_id] = false
      } else {
        res[_id] = true
      }
    }
    return {res}
  }

  // 子服务包状态变化，检查主服务包状态
  async checkMaterPackUser(pid: String, params?: TxnParams) {
    const options = Acan.getTxnOptions(params)
    // 子服务包失效的情况
    const doc: any = await this.Model.findById(pid, null, options)
    const count = await this.Model.countDocuments({pid, status: true}, options)
    if (count === 0 && doc.status === true) {
      // 所有的子服务包已经失效，需要更新主服务包的状态为失效
      await this.Model.updateOne({_id: pid}, {$set: {status: false}}, options)
      await this.app.service('service-pack').incCount(doc.snapshot._id, {'count.valid': -1}, params)
    } else if (count > 0 && doc.status === false) {
      // 子服务包存在有效，需要更新主服务包的状态为激活
      await this.Model.updateOne({_id: pid}, {$set: {status: true}}, options)
      await this.app.service('service-pack').incCount(doc.snapshot._id, {'count.valid': 1}, params)
    }
  }

  // 更新地址,经纬度
  async patchLocation({_id, place_id, address}: any, params: Params) {
    // 改学校管理员可更新
    const uid = params.user?._id
    let packUserData: any = await this.Model.findOne({_id})
    let schoolUser: any = await this.app.service('school-user').Model.findOne({uid, school: packUserData.uid})
    if (!schoolUser || !schoolUser.role.includes('admin')) {
      return Promise.reject(new Error('You are not authorized to update this service pack'))
    }

    this.app.service('campus-location').updateLocation({_id, place_id, model: 'service-pack-user'})
    return await this.Model.updateOne({_id}, {place_id, address})
  }
}
