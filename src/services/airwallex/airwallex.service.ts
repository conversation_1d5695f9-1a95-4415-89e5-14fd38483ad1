import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {Airwallex} from './airwallex.class'
import hooks from './airwallex.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    airwallex: Airwallex & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate'),
  }

  // Initialize our service with any options it requires
  app.use('/airwallex', new Airwallex(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('airwallex')

  service.hooks(hooks)
}
