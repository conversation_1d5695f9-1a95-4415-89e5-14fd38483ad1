import {Application} from '../../declarations'
import {Forbidden, BadRequest, GeneralError} from '@feathersjs/errors'
import {Id, Params, ServiceMethods} from '@feathersjs/feathers'
import {Service} from 'feathers-mongoose'
import axios, {AxiosInstance, AxiosRequestConfig} from 'axios'

interface AirwallexBeneficiary {
  id?: string
  account_name: string
  account_number: string
  swift_code?: string
  aba_routing_number?: string
  bank_country_code: string
  account_currency: string
  bank_name?: string
  address?: {
    country_code: string
    state?: string
    city?: string
    street_address?: string
    postcode?: string
  }
  entity_type: 'INDIVIDUAL' | 'COMPANY'
  first_name?: string
  last_name?: string
  company_name?: string
  date_of_birth?: string
  additional_info?: any
}

interface CreateBeneficiaryPayload {
  userId?: string
  schoolId?: string
  beneficiaryData: {values: AirwallexBeneficiary}
}

interface AirwallexConfig {
  apiKey: string
  clientId: string
  baseUrl: string
  environment: 'demo' | 'prod'
}
interface Data {}
interface ServiceOptions {}

export class Airwallex implements ServiceMethods<Data> {
  app: Application
  options: any

  private axiosInstance: AxiosInstance
  private config: AirwallexConfig

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options
    this.app = app
    this.config = {
      apiKey: '79b11d238f1efc90d93e339b58ee53a5b7cdd5cc0d76eca628800b87e95aeac2525b7f1e2e42952f81f739419aec426f',
      clientId: 'wWruBiv_SL2dU_Oe7N5ZKQ',
      baseUrl: 'https://api-demo.airwallex.com', // Default to demo
      environment: 'demo',
    }

    this.axiosInstance = axios.create({
      baseURL: this.config.baseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  async get(id: Id, params: Params): Promise<any> {
    if (id === 'auth-code') {
      return this.generateAuthCode(params)
    }

    if (id === 'beneficiary') {
      return this.getBeneficiary(params)
    }
  }

  async create(data: any, params: Params): Promise<any> {
    const {type} = data

    if (type === 'beneficiary') {
      return this.createBeneficiary(data, params)
    }
    throw new BadRequest(`Invalid creation type: ${type}`)
  }

  async patch(id: string, data: any, params: Params): Promise<any> {
    if (id === 'beneficiary') {
      return this.updateBeneficiary(data, params)
    }
  }

  async find(params: Params): Promise<any> {
    return []
  }

  async update(id: string, data: any, params: Params): Promise<any> {
    return id
  }

  async remove(id: string, params: Params): Promise<any> {
    if (id === 'beneficiary') {
      return this.deleteBeneficiary(params)
    }
  }

  /**
   * Authenticate with Airwallex and get access token
   */
  private async authenticate(): Promise<string | null> {
    try {
      const redis = this.app.get('redis')
      const redisKey = 'airwallex_token'

      let cachedToken = await redis.get(redisKey)
      if (cachedToken) {
        this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${cachedToken}`
        return cachedToken
      }

      try {
        const response = await axios({
          method: 'post',
          url: 'https://api-demo.airwallex.com/api/v1/authentication/login',
          headers: {
            'x-client-id': this.config.clientId,
            'x-api-key': this.config.apiKey,
          },
          data: '',
        })

        const {token, expires_at} = response.data
        if (!token || !expires_at) {
          throw new GeneralError('Invalid authentication response from Airwallex')
        }

        const expiryTime = new Date(expires_at).getTime()
        const now = Date.now()
        const expiresInSeconds = Math.floor((expiryTime - now) / 1000) - 60

        if (expiresInSeconds > 0) {
          await redis.set(redisKey, token, {EX: expiresInSeconds})
        }

        if (token) {
          this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${token}`
        } else {
          throw new GeneralError('Failed to obtain a valid token for Airwallex.')
        }

        return token
      } catch (error: any) {
        console.error('Failed to authenticate with Airwallex:', error.response?.data || error.message)
        throw new GeneralError(`Airwallex Authentication Error: ${error.response?.data?.message || error.message || 'Unknown error'}`)
      }
      // Update axios instance with new token
      // this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${this.accessToken}`
    } catch (error: any) {
      if (error.response?.data) {
        throw new GeneralError(`Airwallex Authentication Error: ${error.response.data.message || error.response.data}`)
      }
      throw new GeneralError('Failed to authenticate with Airwallex')
    }
  }

  /**
   * Generate authentication code for embedded components
   */
  async generateAuthCode(params: Params): Promise<any> {
    try {
      const user = params.user
      const schoolId = params.query?.schoolId
      if (!user) {
        throw new Forbidden('Authentication required')
      }

      if (schoolId) {
        await this.verifySchoolPermission(schoolId, user)
      }

      // Authenticate with Airwallex
      await this.authenticate()

      // Generate code verifier for PKCE
      const codeVerifier = this.generateCodeVerifier()

      // Generate code challenge for PKCE
      const codeChallenge = await this.generateCodeChallenge(codeVerifier)

      const requestBody = {
        code_challenge: codeChallenge,
        identity: schoolId ? `school_${schoolId}` : `user_${user._id}`,
        scope: ['w:awx_action:transfers_edit'],
      }

      // Generate authorization code for embedded components
      const response = await this.axiosInstance.post('/api/v1/authentication/authorize', requestBody)

      if (!response.data) {
        throw new GeneralError('Failed to generate authentication code')
      }

      return {
        success: true,
        auth_code: response.data.authorization_code,
        environment: this.config.environment,
        code_verifier: codeVerifier,
        client_id: this.config.clientId,
      }
    } catch (error: any) {
      if (error.response?.data) {
        throw new GeneralError(`Airwallex API Error: ${error.response.data.message || error.response.data}`)
      }
      throw error
    }
  }

  /**
   * Create a beneficiary in Airwallex
   */

  async createBeneficiary(payload: CreateBeneficiaryPayload, params: Params): Promise<any> {
    try {
      const {schoolId} = payload

      const user = params.user
      if (!user) {
        throw new Forbidden('Authentication required')
      }
      if (schoolId) {
        await this.verifySchoolPermission(schoolId, user)
      }

      const existingAccount = await this.app.service('payout-accounts').Model.findOne({
        ...(payload.schoolId ? {schoolId: payload.schoolId} : {userId: user._id}),
        isActive: true,
      })

      if (existingAccount) {
        throw new BadRequest('Payout account already exists for this user/school')
      }

      // Validate beneficiary data
      await this.validateBeneficiaryData(payload.beneficiaryData.values)

      // Authenticate with Airwallex
      await this.authenticate()

      // console.log('this.axiosInstance.defaults.headers.common', this.axiosInstance.defaults.headers.common)
      // Create beneficiary in Airwallex
      const airwallexResponse = await this.axiosInstance.post('/api/v1/beneficiaries/create', payload.beneficiaryData.values)

      if (!airwallexResponse.data || !airwallexResponse.data.beneficiary_id) {
        throw new GeneralError('Failed to create beneficiary in Airwallex')
      }
      // Create payout account record
      const payoutAccountData = {
        ...(schoolId ? {schoolId} : {userId: user._id}),
        airwallexBeneficiaryId: airwallexResponse.data.beneficiary_id,
      }

      // TODO
      const payoutAccount = {}
      await this.app.service('payout-accounts').create(payoutAccountData)

      return {
        success: true,
        payoutAccount,
        airwallexBeneficiary: airwallexResponse.data,
      }
    } catch (error: any) {
      if (error.response) {
        const message = error.response.data.message || error.response.data.error_description || error.response.data.error || JSON.stringify(error.response.data)
        throw new GeneralError(`Airwallex API Error: ${message}`)
      }
      throw error
    }
  }

  /**
   * Get beneficiary details from Airwallex
   */
  async getBeneficiary(params: Params): Promise<any> {
    try {
      const user = params.user
      const schoolId = params.query?.schoolId
      if (!user) {
        throw new Forbidden('Authentication required')
      }
      if (schoolId) {
        await this.verifySchoolPermission(schoolId, user)
      }

      const payoutAccount = await this.app.service('payout-accounts').Model.findOne({
        ...(schoolId ? {schoolId: schoolId} : {userId: user._id}),
        isActive: true,
      })

      if (!payoutAccount || !payoutAccount.airwallexBeneficiaryId) {
        throw new BadRequest('Beneficiary not found')
      }

      const beneficiaryId = payoutAccount.airwallexBeneficiaryId

      // Authenticate with Airwallex
      await this.authenticate()

      // Get beneficiary from Airwallex
      const response = await this.axiosInstance.get(`/api/v1/beneficiaries/${beneficiaryId}`)
      const beneficiaryDetails = response.data
      return response.data
    } catch (error: any) {
      if (error.response?.data) {
        throw new GeneralError(`Airwallex API Error: ${error.response.data.message || error.response.data.error}`)
      }
      throw error
    }
  }

  /**
   * Update beneficiary in Airwallex
   */
  async updateBeneficiary(data: any = {}, params: Params): Promise<any> {
    try {
      const user = params.user
      const schoolId = params.query?.schoolId
      if (!user) {
        throw new Forbidden('Authentication required')
      }
      if (schoolId) {
        await this.verifySchoolPermission(schoolId, user)
      }

      const payoutAccount = await this.app.service('payout-accounts').Model.findOne({
        ...(schoolId ? {schoolId: schoolId} : {userId: user._id}),
        isActive: true,
      })

      if (!payoutAccount || !payoutAccount.airwallexBeneficiaryId) {
        throw new BadRequest('Beneficiary not found')
      }

      const beneficiaryId = payoutAccount.airwallexBeneficiaryId

      // Authenticate with Airwallex
      await this.authenticate()

      await this.validateBeneficiaryData(data.values)

      const response = await this.axiosInstance.post(`/api/v1/beneficiaries/update/${beneficiaryId}`, data.values)

      // Update local record
      await this.app.service('payout-accounts').patch(payoutAccount._id, {}, {user})

      return {
        success: true,
        beneficiary: response.data,
      }
    } catch (error: any) {
      if (error.response?.data) {
        throw new GeneralError(`Airwallex API Error: ${error.response.data.message || error.response.data.error}`)
      }
      throw error
    }
  }

  /**
   * Delete/deactivate beneficiary
   */
  async deleteBeneficiary(params: Params): Promise<any> {
    try {
      const user = params.user
      const schoolId = params.query?.schoolId
      if (!user) {
        throw new Forbidden('Authentication required')
      }
      if (schoolId) {
        await this.verifySchoolPermission(schoolId, user)
      }

      const payoutAccount = await this.app.service('payout-accounts').Model.findOne({
        ...(schoolId ? {schoolId: schoolId} : {userId: user._id}),
        isActive: true,
      })

      if (!payoutAccount) {
        throw new BadRequest('Beneficiary not found')
      }

      // Soft delete the payout account (don't delete from Airwallex as it might be referenced in transactions)
      await this.app.service('payout-accounts').Model.updateOne(
        {_id: payoutAccount._id},
        {
          isActive: false,
          accountStatus: 'inactive',
          updatedAt: new Date(),
        }
      )

      return {
        success: true,
        message: 'Beneficiary deactivated successfully',
      }
    } catch (error: any) {
      throw error
    }
  }

  /**
   * Verify school permission for admin users
   */
  async verifySchoolPermission(schoolId: string, user: any): Promise<void> {
    if (!user) {
      throw new Forbidden('You must be logged in to perform this action.')
    }

    const schoolUser = await this.app.service('school-user').Model.findOne({
      school: schoolId,
      uid: user._id,
      status: 2,
      role: 'admin',
    })

    if (!schoolUser) {
      throw new Forbidden('Not authorized to perform operations for this school')
    }
  }

  // Generate code verifier (43-128 character random string)
  private generateCodeVerifier(): string {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return btoa(String.fromCharCode.apply(null, Array.from(array)))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  // Generate code challenge (SHA256 hash of verifier, base64url encoded)
  private async generateCodeChallenge(verifier: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(verifier)
    const digest = await crypto.subtle.digest('SHA-256', data)
    return btoa(String.fromCharCode.apply(null, Array.from(new Uint8Array(digest))))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  /**
   * Validate beneficiary data
   */
  private async validateBeneficiaryData(data: any): Promise<void> {
    try {
      await this.axiosInstance.post('/api/v1/beneficiaries/validate', data.values)
    } catch (error: any) {
      console.log('validateBeneficiaryData error', error.response?.data)
    }
  }

  /**
   * Get beneficiary requirements for a specific country and currency
   */
  async getBeneficiaryRequirements(countryCode: string, currency: string): Promise<any> {
    try {
      // Authenticate with Airwallex
      await this.authenticate()

      const response = await this.axiosInstance.get('/api/v1/beneficiaries/requirements', {
        params: {
          bank_country_code: countryCode,
          account_currency: currency,
        },
      })

      return {
        success: true,
        requirements: response.data,
      }
    } catch (error: any) {
      if (error.response?.data) {
        throw new GeneralError(`Airwallex API Error: ${error.response.data.message || error.response.data.error}`)
      }
      throw error
    }
  }

  /**
   * Validate beneficiary data against requirements
   */
  async validateBeneficiaryAgainstRequirements(beneficiaryData: AirwallexBeneficiary): Promise<any> {
    try {
      // Authenticate with Airwallex
      await this.authenticate()

      const response = await this.axiosInstance.post('/api/v1/beneficiaries/validate', beneficiaryData)

      return {
        success: true,
        validation: response.data,
      }
    } catch (error: any) {
      if (error.response?.data) {
        return {
          success: false,
          errors: error.response.data.errors || [error.response.data.message || 'Validation failed'],
        }
      }
      throw error
    }
  }

  /**
   * Get supported banks for a specific country
   */
  async getSupportedBanks(countryCode: string): Promise<any> {
    try {
      // Authenticate with Airwallex
      await this.authenticate()

      const response = await this.axiosInstance.get('/api/v1/beneficiaries/banks', {
        params: {
          bank_country_code: countryCode,
        },
      })

      return {
        success: true,
        banks: response.data,
      }
    } catch (error: any) {
      if (error.response?.data) {
        throw new GeneralError(`Airwallex API Error: ${error.response.data.message || error.response.data.error}`)
      }
      throw error
    }
  }
}
