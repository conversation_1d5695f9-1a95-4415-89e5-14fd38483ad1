import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks

const mod: any = {
  async checkStripePayment(d: HookContext) {
    d.result = await d.service.getCheckStripePayment(d.params.query, d.params)
  },
}

export default {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [
      (d: HookContext) => {
        if (d.id && mod[d.id]) return mod[d.id](d)
      },
    ],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
