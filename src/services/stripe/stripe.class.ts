import {Id, NullableId, Paginated, Params, ServiceMethods} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import stripe from './stripe.client'
import {NotAuthenticated} from '@feathersjs/errors'
const {GeneralError, BadRequest, Forbidden} = require('@feathersjs/errors')

interface Data {}

interface PatchIntent {
  clientSecret: string
  setupClientSecret?: string
  paymentMethodId: string
  setupFutureUsage: boolean
}

interface ServiceOptions {}

export class Stripe implements ServiceMethods<Data> {
  app: Application
  options: ServiceOptions

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options
    this.app = app
  }

  async find(params: Params): Promise<Data[] | Paginated<Data>> {
    return []
  }

  async get(id: Id, params: Params): Promise<Data> {
    if (id === 'client-secret') {
      return this.createPaymentIntent(params.query, params)
    }

    return {id}
  }

  async create(data: any, params: Params): Promise<Data> {
    if (data.type === 'createSetupIntent') {
      return this.createSetupIntent(data, params)
    }

    throw new BadRequest('Invalid operation')
  }

  async update(id: NullableId, data: Data, params: Params): Promise<Data> {
    return data
  }

  async patch(id: NullableId, data: any, params: Params): Promise<Data> {
    if (id === 'payment-intent') {
      return this.patchPaymentIntent(data, params)
    }

    return data
  }

  async remove(id: NullableId, params: Params): Promise<Data> {
    return {id}
  }

  /**
   * todo
   * only main admins should have access
   */
  async verifySchoolPermission(schoolId: string, user: any): Promise<void> {
    // Check if user is associated with this school and is an admin
    const schoolUser = await this.app.service('school-user').Model.findOne({
      school: schoolId,
      uid: user._id,
      status: 2,
      role: 'admin',
    })
    console.log('schoolUser', schoolUser)
    if (!schoolUser) {
      throw new Forbidden('Not authorized to perform operations for this school')
    }
  }

  async getPaymentIntent(paymentIntentId: string): Promise<any> {
    if (!paymentIntentId) {
      throw new BadRequest('Payment Intent ID is required')
    }

    try {
      const result = await stripe.paymentIntents.retrieve(paymentIntentId)
      return result
    } catch (error) {
      throw error
    }
  }

  /**
   * Create a payment intent for a new order
   **/
  async createPaymentIntent({orderId}: any, params: any): Promise<any> {
    if (!orderId) {
      throw new BadRequest('Order ID is required')
    }

    const order = await this.app.service('order').get(orderId)

    if (!order || order.status !== 100) {
      throw new BadRequest('Invalid order')
    }

    const orderPrice = order.priceBreakdown?.cash ?? order.price

    // Check if order already has a PaymentIntent ID and it's not expired
    if (order.stripeId) {
      try {
        const existingIntent = await stripe.paymentIntents.retrieve(order.stripeId)
        console.log('existingIntent', existingIntent?.status)
        if (existingIntent.status !== 'canceled' && existingIntent.amount === orderPrice && !existingIntent.canceled_at) {
          return {
            clientSecret: existingIntent.client_secret,
          }
        }
        // Otherwise, we'll create a new one below
      } catch (error) {}
    }

    // Create a new payment intent
    const amount = orderPrice
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency: 'usd',
      metadata: {
        orderId: orderId,
        userId: params.user?._id,
        schoolId: order.isSchool ? order.buyer : undefined,
      },
      automatic_payment_methods: {
        enabled: true,
      },
    })

    // Store the payment intent ID with the order
    await this.app.service('order').patch(orderId, {
      stripeId: paymentIntent.id,
    })

    return {
      clientSecret: paymentIntent.client_secret,
    }
  }

  /**
   * @clientSecret payment intent client secret
   * @setupClientSecret send if we want to save the card
   * @paymentMethodId payment method id
   * @setupFutureUsage true: pay with new card & save it for future use; false: pay using already saved card
   */
  async patchPaymentIntent(data: PatchIntent, params: any): Promise<any> {
    const {clientSecret, setupClientSecret, paymentMethodId, setupFutureUsage = false} = data
    console.log('patchPaymentIntent', clientSecret, setupClientSecret, paymentMethodId, setupFutureUsage)
    const userId = params.user?._id
    if (!userId) {
      throw new NotAuthenticated('User ID is required')
    }
    if (!clientSecret) {
      throw new BadRequest('Client secret is required')
    }

    if (!paymentMethodId) {
      throw new BadRequest('Payment method ID is required')
    }

    if (setupFutureUsage && !setupClientSecret) {
      throw new BadRequest('Setup client secret is required')
    }

    const paymentIntentId = clientSecret.split('_secret_')[0]
    const setupIntentId = setupClientSecret ? setupClientSecret.split('_secret_')[0] : ''

    try {
      const promises: any[] = [stripe.paymentIntents.retrieve(paymentIntentId)]
      if (setupFutureUsage) {
        promises.push(stripe.setupIntents.retrieve(setupIntentId))
      }
      const [paymentIntent, setupIntent] = await Promise.all(promises)
      console.log('paymentIntent?.metadata?.orderId', paymentIntent?.metadata?.orderId)
      if (!paymentIntent?.metadata?.orderId) {
        throw new BadRequest('Invalid payment intent')
      }

      /**
       * todo??
       * check user has access to orderId
       *
       */
      console.log('setupIntent?.metadata?.userId', setupIntent?.metadata?.userId)
      if (setupFutureUsage && setupIntent?.metadata?.userId !== userId) {
        throw new Error('Unauthorized access')
      }

      console.log('setupIntent.customer', setupIntent?.customer)
      if (setupFutureUsage && !setupIntent?.customer) {
        throw new BadRequest('Customer ID is required for saving payment method')
      }

      const payload: Record<string, any> = {
        payment_method: paymentMethodId,
      }

      if (!setupFutureUsage) {
        const paymentMethod: any = await this.app.service('payment-methods').Model.findOne({stripePaymentMethodId: paymentMethodId})
        if (!paymentMethod) {
          throw new BadRequest('Invalid payment method')
        }
        console.log('paymentMethod', JSON.stringify(paymentMethod))
        if (paymentMethod.schoolId) {
          await this.verifySchoolPermission(paymentMethod.schoolId, params.user)
        } else if (paymentMethod.userId !== userId.toString()) {
          throw new Forbidden('Not authorized to access this payment method')
        }
        payload['customer'] = paymentMethod.stripeCustomerId
      }

      if (setupFutureUsage) {
        payload['setup_future_usage'] = setupFutureUsage
        payload['customer'] = setupIntent.customer
      }
      console.log('payload', JSON.stringify(payload))
      const updatedPaymentIntent = await stripe.paymentIntents.update(paymentIntentId, payload)

      return {
        clientSecret: updatedPaymentIntent.client_secret,
      }
    } catch (error: any) {
      console.error(`Error updating payment intent: ${error.message}`)
      throw new GeneralError(`Failed to update payment intent: ${error.message}`)
    }
  }

  async cancelPaymentIntent({paymentIntentId}: any): Promise<any> {
    if (!paymentIntentId) {
      return
    }

    try {
      const canceledPaymentIntent = await stripe.paymentIntents.cancel(paymentIntentId)

      return canceledPaymentIntent
    } catch (error: any) {
      console.error(`Error canceling payment intent: ${error.message}`)
    }
  }

  /**
   * Create a setup intent for saving a card
   * @schoolId if provided, we are managing school's card
   */
  async createSetupIntent({schoolId, setupFutureUsage}: any, params: any): Promise<any> {
    // todo-check
    if (schoolId) {
      await this.verifySchoolPermission(schoolId, params.user)
    }
    let customerId: string | null
    if (schoolId) {
      customerId = await this.getCustomerId(null, schoolId)
    } else {
      customerId = await this.getCustomerId(params.user?._id, null)
    }

    if (!customerId) {
      const customer = await this.createCustomer({userId: params.user?._id, schoolId}, params)
      customerId = customer.id as string
    }
    console.log('setupIntent_customerId', customerId)
    const setupIntent = await stripe.setupIntents.create({
      customer: customerId,
      payment_method_types: ['card'],
      usage: setupFutureUsage,
      metadata: {
        userId: params.user?._id,
        schoolId: schoolId,
      },
    })
    console.log('setupIntent', JSON.stringify(setupIntent))

    return {
      clientSecret: setupIntent.client_secret,
    }
  }

  // get stripe customer id for a user or school
  async getCustomerId(userId: string | null, schoolId: string | null): Promise<string | null> {
    try {
      const query: any = {}

      if (userId) {
        query.userId = userId
      } else if (schoolId) {
        query.schoolId = schoolId
      } else {
        return null
      }

      query.stripePaymentMethodId = 'customer_record'

      const record = (await this.app.service('payment-methods').Model.findOne(query)) as any
      return record ? record.stripeCustomerId : null
    } catch (error) {
      console.error('Error retrieving customer ID:', error)
      return null
    }
  }

  // Create a stripe customer for a user or school
  async createCustomer({userId, schoolId}: any, params: any): Promise<any> {
    if (schoolId) {
      const school = await this.app.service('school-plan').get(schoolId)

      const customer = await stripe.customers.create({
        name: school.name,
        email: school.email || params.user.email,
        metadata: {
          schoolId: schoolId,
          createdBy: params.user?._id,
        },
      })

      await this.app.service('payment-methods').Model.create({
        schoolId: schoolId,
        userId: null,
        stripeCustomerId: customer.id,
        stripePaymentMethodId: 'customer_record', // Special marker
        cardBrand: 'N/A',
        last4: 'N/A',
        expMonth: 1,
        expYear: 2099,
        fingerprint: 'customer_record',
        isDefault: true,
      })

      return customer
    } else {
      const user = await this.app.service('users').get(userId || params.user?._id)

      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name.join(' '),
        metadata: {
          userId: user._id,
        },
      })

      await this.app.service('payment-methods').Model.create({
        userId: user._id,
        schoolId: null,
        stripeCustomerId: customer.id,
        stripePaymentMethodId: 'customer_record', // Special marker
        cardBrand: 'N/A',
        last4: 'N/A',
        expMonth: 1,
        expYear: 2099,
        fingerprint: 'customer_record',
        isDefault: true,
      })

      return customer
    }
  }

  async getPaymentMethod(paymentMethodId: string, customerId: string | null): Promise<any> {
    if (!paymentMethodId) {
      throw new BadRequest('Payment method ID is required')
    }

    if (!customerId) {
      throw new BadRequest('Customer ID is required')
    }

    const result = await stripe.customers.retrievePaymentMethod(customerId, paymentMethodId)

    return result
  }

  // Attach a payment method to a customer
  async attachPaymentMethod({paymentMethodId, customerId}: any): Promise<any> {
    if (!paymentMethodId || !customerId) {
      throw new BadRequest('Payment method ID and customer ID are required')
    }

    const attachedPaymentMethod = await stripe.paymentMethods.attach(paymentMethodId, {customer: customerId})

    return attachedPaymentMethod
  }

  // Detach a payment method from a customer
  async detachPaymentMethod({paymentMethodId}: any): Promise<any> {
    if (!paymentMethodId) {
      throw new BadRequest('Payment method ID is required')
    }

    return await stripe.paymentMethods.detach(paymentMethodId)
  }

  // Set a payment method as default for a customer
  async setDefaultPaymentMethod({paymentMethodId, customerId}: any): Promise<any> {
    if (!paymentMethodId || !customerId) {
      throw new BadRequest('Payment method ID and customer ID are required')
    }

    await stripe.customers.update(customerId, {
      invoice_settings: {default_payment_method: paymentMethodId},
    })
  }

  async createRefund({paymentIntentId, amount}: {paymentIntentId: string; amount: number}): Promise<any> {
    if (!paymentIntentId) {
      throw new BadRequest('Payment Intent ID is required')
    }

    try {
      const refund = await stripe.refunds.create({
        payment_intent: paymentIntentId,
        amount: amount,
      })

      return refund
    } catch (error: any) {
      const errorMessage = error.message || 'Error processing refund'
      throw new GeneralError(errorMessage)
    }
  }

  // to verify payment from frontend
  async getCheckStripePayment({stripePaymentIntentId}: any) {
    let stripeDetail
    try {
      stripeDetail = await this.getPaymentIntent(stripePaymentIntentId)
    } catch (error) {
      return Promise.reject(new GeneralError(error))
    }
    if (stripeDetail.status === 'succeeded') {
      if (!stripeDetail.metadata.orderId) return false
      await this.app.service('order').processOrderCompletion(
        stripeDetail.metadata.orderId,
        {
          settled: true,
          $push: {
            payMethod: 'stripe',
            paymentRecords: {
              method: 'stripe',
              status: 'paid',
              amount: stripeDetail.amount,
              transactionId: stripePaymentIntentId,
              paidAt: new Date(),
            },
          },
          paidAt: new Date(),
        },
        'api'
      )
      return true
    } else {
      return false
    }
  }
}
