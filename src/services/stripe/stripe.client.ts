import Stripe from 'stripe'

// Import configuration
const stripeConfig = require('../../../config/stripe.json')
const config = isDev ? stripeConfig.sandbox : stripeConfig.live

// Use environment variables if available, otherwise fall back to config file
const apiKey = isDev ? process.env.STRIPE_SECRET_KEY_TEST || config.secretKey : process.env.STRIPE_SECRET_KEY_LIVE || config.secretKey

if (!apiKey) {
  throw new Error('Stripe API key is missing. Please check your environment variables.')
}

// Create a singleton instance
const stripe = new Stripe(apiKey, {
  apiVersion: '2025-04-30.basil', // Use a version supported by your Stripe package
  appInfo: {
    name: 'YourAppName',
    version: '1.0.0',
  },
})

export default stripe
