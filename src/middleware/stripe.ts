import {Request, Response} from 'express'
import stripe from '../services/stripe/stripe.client'
const stripeSettings = require('../../config/stripe.json')

export default function (app: any): void {
  app.post('/stripe/webhook', async (req: Request, res: Response) => {
    const sig = req.headers['stripe-signature'] as string
    let event

    try {
      const stripeSetting = !isDev ? stripeSettings.live : stripeSettings.sandbox

      // for local development, use stripe-cli
      // command to run: stripe listen --forward-to localhost:3030/stripe/webhook
      // In webhookSecret use the secret provided after running stripe listen
      const webhookSecret = !isDev ? stripeSetting.webhookSecret : 'REPLACE_WITH_YOUR_SECRET_FROM_STRIPE_CLI'

      event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret)

      if (['payment_intent.succeeded', 'charge.refunded'].includes(event.type)) {
        await app.service('stripe-webhook').Model.create({
          eventId: event.id,
          eventType: event.type,
          data: event.data,
        })
      }
    } catch (err: any) {
      return res.status(401).send(`Webhook Error: ${err.message}`)
    }

    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object, app)
        break

      case 'payment_method.detached':
        if (event.data.object.id) {
          await app.service('payment-methods').Model.deleteOne({
            stripePaymentMethodId: event.data.object.id,
          })
        }
        break

      default:
        console.log(`Unhandled event type ${event.type}`)
    }

    res.json({received: true})
  })
}

async function handlePaymentIntentSucceeded(paymentIntent: any, app: any) {
  const {orderId} = paymentIntent.metadata

  if (!orderId) {
    return
  }

  await app.service('order').processOrderCompletion(orderId, {
    settled: true,
    $push: {
      payMethod: 'stripe',
      paymentRecords: {
        method: 'stripe',
        status: 'paid',
        amount: paymentIntent.amount,
        transactionId: paymentIntent.id,
        paidAt: new Date(),
      },
    },
    paidAt: new Date(),
  })
}
